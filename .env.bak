APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:j0YXM14txrKFvWgmOcq1l/gVhj1F6HZer/YsN4EwMS4=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

APP_LOCALE=vi
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=job-survey-2
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=file

CACHE_STORE=file
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"


SSO_URL="https://st-dse.vnua.edu.vn:6891"
SSO_CLIENT_ID="9ed66909-b63b-4cf7-89b4-f62644ffe132"
SSO_CLIENT_SECRET="ojTZ6DXLT41xFi4tl1qJO50gbAHKDas83HOWoarS"
SSO_REDIRECT_URL="http://127.0.0.1:8000/auth/callback"

SSO_IP="https://st-dse.vnua.edu.vn:6891"

# # External API System
# EXTERNAL_API_BASE_URL="https://st-dse.vnua.edu.vn:6891/api"
# st.student
STUDENT_URL=https://st-dse.vnua.edu.vn:6899
STUDENT_IP=https://st-dse.vnua.edu.vn:6899
# STUDENT_IP=http://127.0.0.1:8001
STUDENT_CLIENT_ID=9f4502c5-b3d4-45b3-b343-ff15f835e37b
STUDENT_CLIENT_SECRET=lv1FtoxieZWbkS2TltTE5iE85OWtxd3dBWQ1x3fA


