!function(t){function e(t){return plupload.translate(t)||t}function i(i,s){s.contents().each(function(e,i){i=t(i),i.is(".plupload")||i.remove()}),s.prepend('<div class="plupload_wrapper plupload_scroll"><div id="'+i+'_container" class="plupload_container">'+'<div class="plupload">'+'<div class="plupload_header">'+'<div class="plupload_header_content">'+'<div class="plupload_header_title">'+e("Select files")+"</div>"+'<div class="plupload_header_text">'+e("Add files to the upload queue and click the start button.")+"</div>"+"</div>"+"</div>"+'<div class="plupload_content">'+'<div class="plupload_filelist_header">'+'<div class="plupload_file_name">'+e("Filename")+"</div>"+'<div class="plupload_file_action">&nbsp;</div>'+'<div class="plupload_file_status"><span>'+e("Status")+"</span></div>"+'<div class="plupload_file_size">'+e("Size")+"</div>"+'<div class="plupload_clearer">&nbsp;</div>'+"</div>"+'<ul id="'+i+'_filelist" class="plupload_filelist"></ul>'+'<div class="plupload_filelist_footer">'+'<div class="plupload_file_name">'+'<div class="plupload_buttons">'+'<a href="#" class="plupload_button plupload_add" id="'+i+'_browse">'+e("Add Files")+"</a>"+'<a href="#" class="plupload_button plupload_start">'+e("Start Upload")+"</a>"+"</div>"+'<span class="plupload_upload_status"></span>'+"</div>"+'<div class="plupload_file_action"></div>'+'<div class="plupload_file_status"><span class="plupload_total_status">0%</span></div>'+'<div class="plupload_file_size"><span class="plupload_total_file_size">0 b</span></div>'+'<div class="plupload_progress">'+'<div class="plupload_progress_container">'+'<div class="plupload_progress_bar"></div>'+"</div>"+"</div>"+'<div class="plupload_clearer">&nbsp;</div>'+"</div>"+"</div>"+"</div>"+"</div>"+'<input type="hidden" id="'+i+'_count" name="'+i+'_count" value="0" />'+"</div>")}var s={};t.fn.pluploadQueue=function(n){return n?(this.each(function(){function a(e){var i;e.state==plupload.DONE&&(i="plupload_done"),e.state==plupload.FAILED&&(i="plupload_failed"),e.state==plupload.QUEUED&&(i="plupload_delete"),e.state==plupload.UPLOADING&&(i="plupload_uploading");var s=t("#"+e.id).attr("class",i).find("a").css("display","block");e.hint&&s.attr("title",e.hint)}function o(){t("span.plupload_total_status",d).html(u.total.percent+"%"),t("div.plupload_progress_bar",d).css("width",u.total.percent+"%"),t("span.plupload_upload_status",d).html(plupload.sprintf(e("Uploaded %d/%d files"),u.total.uploaded,u.files.length))}function l(){var i,s=t("ul.plupload_filelist",d).html(""),n=0;t.each(u.files,function(e,o){i="",o.state==plupload.DONE&&(o.target_name&&(i+='<input type="hidden" name="'+p+"_"+n+'_tmpname" value="'+plupload.xmlEncode(o.target_name)+'" />'),i+='<input type="hidden" name="'+p+"_"+n+'_name" value="'+plupload.xmlEncode(o.name)+'" />',i+='<input type="hidden" name="'+p+"_"+n+'_status" value="'+(o.state==plupload.DONE?"done":"failed")+'" />',n++,t("#"+p+"_count").val(n)),s.append('<li id="'+o.id+'">'+'<div class="plupload_file_name"><span>'+plupload.xmlEncode(o.name)+"</span></div>"+'<div class="plupload_file_action"><a href="#"></a></div>'+'<div class="plupload_file_status">'+o.percent+"%</div>"+'<div class="plupload_file_size">'+plupload.formatSize(o.size)+"</div>"+'<div class="plupload_clearer">&nbsp;</div>'+i+"</li>"),a(o),t("#"+o.id+".plupload_delete a").click(function(e){t("#"+o.id).remove(),u.removeFile(o),e.preventDefault()})}),t("span.plupload_total_file_size",d).html(plupload.formatSize(u.total.size)),0===u.total.queued?t("span.plupload_add_text",d).html(e("Add Files")):t("span.plupload_add_text",d).html(plupload.sprintf(e("%d files queued"),u.total.queued)),t("a.plupload_start",d).toggleClass("plupload_disabled",u.files.length==u.total.uploaded+u.total.failed),s[0].scrollTop=s[0].scrollHeight,o(),!u.files.length&&u.features.dragdrop&&u.settings.dragdrop&&t("#"+p+"_filelist").append('<li class="plupload_droptext">'+e("Drag files here.")+"</li>")}function r(){delete s[p],u.destroy(),d.html(c),u=d=c=null}var u,d,p,c;d=t(this),p=d.attr("id"),p||(p=plupload.guid(),d.attr("id",p)),c=d.html(),i(p,d),n=t.extend({dragdrop:!0,browse_button:p+"_browse",container:p},n),n.dragdrop&&(n.drop_element=p+"_filelist"),u=new plupload.Uploader(n),s[p]=u,u.bind("UploadFile",function(e,i){t("#"+i.id).addClass("plupload_current_file")}),u.bind("Init",function(e,i){!n.unique_names&&n.rename&&d.on("click","#"+p+"_filelist div.plupload_file_name span",function(i){var s,n,a,o=t(i.target),l="";s=e.getFile(o.parents("li")[0].id),a=s.name,n=/^(.+)(\.[^.]+)$/.exec(a),n&&(a=n[1],l=n[2]),o.hide().after('<input type="text" />'),o.next().val(a).focus().blur(function(){o.show().next().remove()}).keydown(function(e){var i=t(this);13==e.keyCode&&(e.preventDefault(),s.name=i.val()+l,o.text(s.name),i.blur())})}),t("#"+p+"_container").attr("title","Using runtime: "+i.runtime),t("a.plupload_start",d).click(function(e){t(this).hasClass("plupload_disabled")||u.start(),e.preventDefault()}),t("a.plupload_stop",d).click(function(t){t.preventDefault(),u.stop()}),t("a.plupload_start",d).addClass("plupload_disabled")}),u.bind("Error",function(i,s){var n,a=s.file;a&&(n=s.message,s.details&&(n+=" ("+s.details+")"),s.code==plupload.FILE_SIZE_ERROR&&alert(e("Error: File too large:")+" "+a.name),s.code==plupload.FILE_EXTENSION_ERROR&&alert(e("Error: Invalid file extension:")+" "+a.name),a.hint=n,t("#"+a.id).attr("class","plupload_failed").find("a").css("display","block").attr("title",n)),s.code===plupload.INIT_ERROR&&setTimeout(function(){r()},1)}),u.bind("PostInit",function(i){i.settings.dragdrop&&i.features.dragdrop&&t("#"+p+"_filelist").append('<li class="plupload_droptext">'+e("Drag files here.")+"</li>")}),u.init(),u.bind("StateChanged",function(){u.state===plupload.STARTED?(t("li.plupload_delete a,div.plupload_buttons",d).hide(),u.disableBrowse(!0),t("span.plupload_upload_status,div.plupload_progress,a.plupload_stop",d).css("display","block"),t("span.plupload_upload_status",d).html("Uploaded "+u.total.uploaded+"/"+u.files.length+" files"),n.multiple_queues&&t("span.plupload_total_status,span.plupload_total_file_size",d).show()):(l(),t("a.plupload_stop,div.plupload_progress",d).hide(),t("a.plupload_delete",d).css("display","block"),n.multiple_queues&&u.total.uploaded+u.total.failed==u.files.length&&(t(".plupload_buttons,.plupload_upload_status",d).css("display","inline"),u.disableBrowse(!1),t(".plupload_start",d).addClass("plupload_disabled"),t("span.plupload_total_status,span.plupload_total_file_size",d).hide()))}),u.bind("FilesAdded",l),u.bind("FilesRemoved",function(){var e=t("#"+p+"_filelist").scrollTop();l(),t("#"+p+"_filelist").scrollTop(e)}),u.bind("FileUploaded",function(t,e){a(e)}),u.bind("UploadProgress",function(e,i){t("#"+i.id+" div.plupload_file_status",d).html(i.percent+"%"),a(i),o()}),n.setup&&n.setup(u)}),this):s[t(this[0]).attr("id")]}}(jQuery);