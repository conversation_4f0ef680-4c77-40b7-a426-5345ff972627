!function(_){_.ui=_.ui||{};_.ui.version="1.13.0";var r,n=0,s=Array.prototype.hasOwnProperty,a=Array.prototype.slice;_.cleanData=(r=_.cleanData,function(e){for(var t,n,i=0;null!=(n=e[i]);i++)(t=_._data(n,"events"))&&t.remove&&_(n).triggerHandler("remove");r(e)}),_.widget=function(e,n,t){var i,r,o,s={},a=e.split(".")[0],l=a+"-"+(e=e.split(".")[1]);return t||(t=n,n=_.Widget),Array.isArray(t)&&(t=_.extend.apply(null,[{}].concat(t))),_.expr.pseudos[l.toLowerCase()]=function(e){return!!_.data(e,l)},_[a]=_[a]||{},i=_[a][e],r=_[a][e]=function(e,t){if(!this._createWidget)return new r(e,t);arguments.length&&this._createWidget(e,t)},_.extend(r,i,{version:t.version,_proto:_.extend({},t),_childConstructors:[]}),(o=new n).options=_.widget.extend({},o.options),_.each(t,function(t,i){function r(){return n.prototype[t].apply(this,arguments)}function o(e){return n.prototype[t].apply(this,e)}s[t]="function"==typeof i?function(){var e,t=this._super,n=this._superApply;return this._super=r,this._superApply=o,e=i.apply(this,arguments),this._super=t,this._superApply=n,e}:i}),r.prototype=_.widget.extend(o,{widgetEventPrefix:i&&o.widgetEventPrefix||e},s,{constructor:r,namespace:a,widgetName:e,widgetFullName:l}),i?(_.each(i._childConstructors,function(e,t){var n=t.prototype;_.widget(n.namespace+"."+n.widgetName,r,t._proto)}),delete i._childConstructors):n._childConstructors.push(r),_.widget.bridge(e,r),r},_.widget.extend=function(e){for(var t,n,i=a.call(arguments,1),r=0,o=i.length;r<o;r++)for(t in i[r])n=i[r][t],s.call(i[r],t)&&void 0!==n&&(_.isPlainObject(n)?e[t]=_.isPlainObject(e[t])?_.widget.extend({},e[t],n):_.widget.extend({},n):e[t]=n);return e},_.widget.bridge=function(o,t){var s=t.prototype.widgetFullName||o;_.fn[o]=function(n){var e="string"==typeof n,i=a.call(arguments,1),r=this;return e?this.length||"instance"!==n?this.each(function(){var e,t=_.data(this,s);return"instance"===n?(r=t,!1):t?"function"!=typeof t[n]||"_"===n.charAt(0)?_.error("no such method '"+n+"' for "+o+" widget instance"):(e=t[n].apply(t,i))!==t&&void 0!==e?(r=e&&e.jquery?r.pushStack(e.get()):e,!1):void 0:_.error("cannot call methods on "+o+" prior to initialization; attempted to call method '"+n+"'")}):r=void 0:(i.length&&(n=_.widget.extend.apply(null,[n].concat(i))),this.each(function(){var e=_.data(this,s);e?(e.option(n||{}),e._init&&e._init()):_.data(this,s,new t(n,this))})),r}},_.Widget=function(){},_.Widget._childConstructors=[],_.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{classes:{},disabled:!1,create:null},_createWidget:function(e,t){t=_(t||this.defaultElement||this)[0],this.element=_(t),this.uuid=n++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=_(),this.hoverable=_(),this.focusable=_(),this.classesElementLookup={},t!==this&&(_.data(t,this.widgetFullName,this),this._on(!0,this.element,{remove:function(e){e.target===t&&this.destroy()}}),this.document=_(t.style?t.ownerDocument:t.document||t),this.window=_(this.document[0].defaultView||this.document[0].parentWindow)),this.options=_.widget.extend({},this.options,this._getCreateOptions(),e),this._create(),this.options.disabled&&this._setOptionDisabled(this.options.disabled),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:function(){return{}},_getCreateEventData:_.noop,_create:_.noop,_init:_.noop,destroy:function(){var n=this;this._destroy(),_.each(this.classesElementLookup,function(e,t){n._removeClass(t,e)}),this.element.off(this.eventNamespace).removeData(this.widgetFullName),this.widget().off(this.eventNamespace).removeAttr("aria-disabled"),this.bindings.off(this.eventNamespace)},_destroy:_.noop,widget:function(){return this.element},option:function(e,t){var n,i,r,o=e;if(0===arguments.length)return _.widget.extend({},this.options);if("string"==typeof e)if(o={},e=(n=e.split(".")).shift(),n.length){for(i=o[e]=_.widget.extend({},this.options[e]),r=0;r<n.length-1;r++)i[n[r]]=i[n[r]]||{},i=i[n[r]];if(e=n.pop(),1===arguments.length)return void 0===i[e]?null:i[e];i[e]=t}else{if(1===arguments.length)return void 0===this.options[e]?null:this.options[e];o[e]=t}return this._setOptions(o),this},_setOptions:function(e){for(var t in e)this._setOption(t,e[t]);return this},_setOption:function(e,t){return"classes"===e&&this._setOptionClasses(t),this.options[e]=t,"disabled"===e&&this._setOptionDisabled(t),this},_setOptionClasses:function(e){var t,n,i;for(t in e)i=this.classesElementLookup[t],e[t]!==this.options.classes[t]&&i&&i.length&&(n=_(i.get()),this._removeClass(i,t),n.addClass(this._classes({element:n,keys:t,classes:e,add:!0})))},_setOptionDisabled:function(e){this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!!e),e&&(this._removeClass(this.hoverable,null,"ui-state-hover"),this._removeClass(this.focusable,null,"ui-state-focus"))},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_classes:function(r){var o=[],s=this;function e(e,t){for(var n,i=0;i<e.length;i++)n=s.classesElementLookup[e[i]]||_(),n=r.add?(r.element.each(function(e,t){_.map(s.classesElementLookup,function(e){return e}).some(function(e){return e.is(t)})||s._on(_(t),{remove:"_untrackClassesElement"})}),_(_.uniqueSort(n.get().concat(r.element.get())))):_(n.not(r.element).get()),s.classesElementLookup[e[i]]=n,o.push(e[i]),t&&r.classes[e[i]]&&o.push(r.classes[e[i]])}return(r=_.extend({element:this.element,classes:this.options.classes||{}},r)).keys&&e(r.keys.match(/\S+/g)||[],!0),r.extra&&e(r.extra.match(/\S+/g)||[]),o.join(" ")},_untrackClassesElement:function(n){var i=this;_.each(i.classesElementLookup,function(e,t){-1!==_.inArray(n.target,t)&&(i.classesElementLookup[e]=_(t.not(n.target).get()))}),this._off(_(n.target))},_removeClass:function(e,t,n){return this._toggleClass(e,t,n,!1)},_addClass:function(e,t,n){return this._toggleClass(e,t,n,!0)},_toggleClass:function(e,t,n,i){var r="string"==typeof e||null===e,n={extra:r?t:n,keys:r?e:t,element:r?this.element:e,add:i="boolean"==typeof i?i:n};return n.element.toggleClass(this._classes(n),i),this},_on:function(r,o,e){var s,a=this;"boolean"!=typeof r&&(e=o,o=r,r=!1),e?(o=s=_(o),this.bindings=this.bindings.add(o)):(e=o,o=this.element,s=this.widget()),_.each(e,function(e,t){function n(){if(r||!0!==a.options.disabled&&!_(this).hasClass("ui-state-disabled"))return("string"==typeof t?a[t]:t).apply(a,arguments)}"string"!=typeof t&&(n.guid=t.guid=t.guid||n.guid||_.guid++);var i=e.match(/^([\w:-]*)\s*(.*)$/),e=i[1]+a.eventNamespace,i=i[2];i?s.on(e,i,n):o.on(e,n)})},_off:function(e,t){t=(t||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,e.off(t),this.bindings=_(this.bindings.not(e).get()),this.focusable=_(this.focusable.not(e).get()),this.hoverable=_(this.hoverable.not(e).get())},_delay:function(e,t){var n=this;return setTimeout(function(){return("string"==typeof e?n[e]:e).apply(n,arguments)},t||0)},_hoverable:function(e){this.hoverable=this.hoverable.add(e),this._on(e,{mouseenter:function(e){this._addClass(_(e.currentTarget),null,"ui-state-hover")},mouseleave:function(e){this._removeClass(_(e.currentTarget),null,"ui-state-hover")}})},_focusable:function(e){this.focusable=this.focusable.add(e),this._on(e,{focusin:function(e){this._addClass(_(e.currentTarget),null,"ui-state-focus")},focusout:function(e){this._removeClass(_(e.currentTarget),null,"ui-state-focus")}})},_trigger:function(e,t,n){var i,r,o=this.options[e];if(n=n||{},(t=_.Event(t)).type=(e===this.widgetEventPrefix?e:this.widgetEventPrefix+e).toLowerCase(),t.target=this.element[0],r=t.originalEvent)for(i in r)i in t||(t[i]=r[i]);return this.element.trigger(t,n),!("function"==typeof o&&!1===o.apply(this.element[0],[t].concat(n))||t.isDefaultPrevented())}},_.each({show:"fadeIn",hide:"fadeOut"},function(o,s){_.Widget.prototype["_"+o]=function(t,e,n){var i,r=(e="string"==typeof e?{effect:e}:e)?!0!==e&&"number"!=typeof e&&e.effect||s:o;"number"==typeof(e=e||{})?e={duration:e}:!0===e&&(e={}),i=!_.isEmptyObject(e),e.complete=n,e.delay&&t.delay(e.delay),i&&_.effects&&_.effects.effect[r]?t[o](e):r!==o&&t[r]?t[r](e.duration,e.easing,n):t.queue(function(e){_(this)[o](),n&&n.call(t[0]),e()})}});var i,k,w,o,l,d,c,u,N;_.widget;function S(e,t,n){return[parseFloat(e[0])*(u.test(e[0])?t/100:1),parseFloat(e[1])*(u.test(e[1])?n/100:1)]}function E(e,t){return parseInt(_.css(e,t),10)||0}function T(e){return null!=e&&e===e.window}k=Math.max,w=Math.abs,o=/left|center|right/,l=/top|center|bottom/,d=/[\+\-]\d+(\.[\d]+)?%?/,c=/^\w+/,u=/%$/,N=_.fn.position,_.position={scrollbarWidth:function(){if(void 0!==i)return i;var e,t=_("<div style='display:block;position:absolute;width:200px;height:200px;overflow:hidden;'><div style='height:300px;width:auto;'></div></div>"),n=t.children()[0];return _("body").append(t),e=n.offsetWidth,t.css("overflow","scroll"),e===(n=n.offsetWidth)&&(n=t[0].clientWidth),t.remove(),i=e-n},getScrollInfo:function(e){var t=e.isWindow||e.isDocument?"":e.element.css("overflow-x"),n=e.isWindow||e.isDocument?"":e.element.css("overflow-y"),t="scroll"===t||"auto"===t&&e.width<e.element[0].scrollWidth;return{width:"scroll"===n||"auto"===n&&e.height<e.element[0].scrollHeight?_.position.scrollbarWidth():0,height:t?_.position.scrollbarWidth():0}},getWithinInfo:function(e){var t=_(e||window),n=T(t[0]),i=!!t[0]&&9===t[0].nodeType;return{element:t,isWindow:n,isDocument:i,offset:!n&&!i?_(e).offset():{left:0,top:0},scrollLeft:t.scrollLeft(),scrollTop:t.scrollTop(),width:t.outerWidth(),height:t.outerHeight()}}},_.fn.position=function(u){if(!u||!u.of)return N.apply(this,arguments);var h,f,p,g,y,e,v="string"==typeof(u=_.extend({},u)).of?_(document).find(u.of):_(u.of),m=_.position.getWithinInfo(u.within),b=_.position.getScrollInfo(m),x=(u.collision||"flip").split(" "),C={},t=9===(e=(t=v)[0]).nodeType?{width:t.width(),height:t.height(),offset:{top:0,left:0}}:T(e)?{width:t.width(),height:t.height(),offset:{top:t.scrollTop(),left:t.scrollLeft()}}:e.preventDefault?{width:0,height:0,offset:{top:e.pageY,left:e.pageX}}:{width:t.outerWidth(),height:t.outerHeight(),offset:t.offset()};return v[0].preventDefault&&(u.at="left top"),f=t.width,p=t.height,y=_.extend({},g=t.offset),_.each(["my","at"],function(){var e,t,n=(u[this]||"").split(" ");(n=1===n.length?o.test(n[0])?n.concat(["center"]):l.test(n[0])?["center"].concat(n):["center","center"]:n)[0]=o.test(n[0])?n[0]:"center",n[1]=l.test(n[1])?n[1]:"center",e=d.exec(n[0]),t=d.exec(n[1]),C[this]=[e?e[0]:0,t?t[0]:0],u[this]=[c.exec(n[0])[0],c.exec(n[1])[0]]}),1===x.length&&(x[1]=x[0]),"right"===u.at[0]?y.left+=f:"center"===u.at[0]&&(y.left+=f/2),"bottom"===u.at[1]?y.top+=p:"center"===u.at[1]&&(y.top+=p/2),h=S(C.at,f,p),y.left+=h[0],y.top+=h[1],this.each(function(){var n,e,s=_(this),a=s.outerWidth(),l=s.outerHeight(),t=E(this,"marginLeft"),i=E(this,"marginTop"),r=a+t+E(this,"marginRight")+b.width,o=l+i+E(this,"marginBottom")+b.height,d=_.extend({},y),c=S(C.my,s.outerWidth(),s.outerHeight());"right"===u.my[0]?d.left-=a:"center"===u.my[0]&&(d.left-=a/2),"bottom"===u.my[1]?d.top-=l:"center"===u.my[1]&&(d.top-=l/2),d.left+=c[0],d.top+=c[1],n={marginLeft:t,marginTop:i},_.each(["left","top"],function(e,t){_.ui.position[x[e]]&&_.ui.position[x[e]][t](d,{targetWidth:f,targetHeight:p,elemWidth:a,elemHeight:l,collisionPosition:n,collisionWidth:r,collisionHeight:o,offset:[h[0]+c[0],h[1]+c[1]],my:u.my,at:u.at,within:m,elem:s})}),u.using&&(e=function(e){var t=g.left-d.left,n=t+f-a,i=g.top-d.top,r=i+p-l,o={target:{element:v,left:g.left,top:g.top,width:f,height:p},element:{element:s,left:d.left,top:d.top,width:a,height:l},horizontal:n<0?"left":0<t?"right":"center",vertical:r<0?"top":0<i?"bottom":"middle"};f<a&&w(t+n)<f&&(o.horizontal="center"),p<l&&w(i+r)<p&&(o.vertical="middle"),k(w(t),w(n))>k(w(i),w(r))?o.important="horizontal":o.important="vertical",u.using.call(this,e,o)}),s.offset(_.extend(d,{using:e}))})},_.ui.position={fit:{left:function(e,t){var n=t.within,i=n.isWindow?n.scrollLeft:n.offset.left,r=n.width,o=e.left-t.collisionPosition.marginLeft,s=i-o,a=o+t.collisionWidth-r-i;t.collisionWidth>r?0<s&&a<=0?(n=e.left+s+t.collisionWidth-r-i,e.left+=s-n):e.left=!(0<a&&s<=0)&&a<s?i+r-t.collisionWidth:i:0<s?e.left+=s:0<a?e.left-=a:e.left=k(e.left-o,e.left)},top:function(e,t){var n=t.within,i=n.isWindow?n.scrollTop:n.offset.top,r=t.within.height,o=e.top-t.collisionPosition.marginTop,s=i-o,a=o+t.collisionHeight-r-i;t.collisionHeight>r?0<s&&a<=0?(n=e.top+s+t.collisionHeight-r-i,e.top+=s-n):e.top=!(0<a&&s<=0)&&a<s?i+r-t.collisionHeight:i:0<s?e.top+=s:0<a?e.top-=a:e.top=k(e.top-o,e.top)}},flip:{left:function(e,t){var n=t.within,i=n.offset.left+n.scrollLeft,r=n.width,o=n.isWindow?n.scrollLeft:n.offset.left,s=e.left-t.collisionPosition.marginLeft,a=s-o,l=s+t.collisionWidth-r-o,d="left"===t.my[0]?-t.elemWidth:"right"===t.my[0]?t.elemWidth:0,n="left"===t.at[0]?t.targetWidth:"right"===t.at[0]?-t.targetWidth:0,s=-2*t.offset[0];a<0?((i=e.left+d+n+s+t.collisionWidth-r-i)<0||i<w(a))&&(e.left+=d+n+s):0<l&&(0<(o=e.left-t.collisionPosition.marginLeft+d+n+s-o)||w(o)<l)&&(e.left+=d+n+s)},top:function(e,t){var n=t.within,i=n.offset.top+n.scrollTop,r=n.height,o=n.isWindow?n.scrollTop:n.offset.top,s=e.top-t.collisionPosition.marginTop,a=s-o,l=s+t.collisionHeight-r-o,d="top"===t.my[1]?-t.elemHeight:"bottom"===t.my[1]?t.elemHeight:0,n="top"===t.at[1]?t.targetHeight:"bottom"===t.at[1]?-t.targetHeight:0,s=-2*t.offset[1];a<0?((i=e.top+d+n+s+t.collisionHeight-r-i)<0||i<w(a))&&(e.top+=d+n+s):0<l&&(0<(o=e.top-t.collisionPosition.marginTop+d+n+s-o)||w(o)<l)&&(e.top+=d+n+s)}},flipfit:{left:function(){_.ui.position.flip.left.apply(this,arguments),_.ui.position.fit.left.apply(this,arguments)},top:function(){_.ui.position.flip.top.apply(this,arguments),_.ui.position.fit.top.apply(this,arguments)}}};var t,h;_.ui.position;_.expr.pseudos||(_.expr.pseudos=_.expr[":"]),_.uniqueSort||(_.uniqueSort=_.unique),_.escapeSelector||(t=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g,h=function(e,t){return t?"\0"===e?"\ufffd":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},_.escapeSelector=function(e){return(e+"").replace(t,h)}),_.fn.even&&_.fn.odd||_.fn.extend({even:function(){return this.filter(function(e){return e%2==0})},odd:function(){return this.filter(function(e){return e%2==1})}});var e;_.ui.keyCode={BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38},_.fn.scrollParent=function(e){var t=this.css("position"),n="absolute"===t,i=e?/(auto|scroll|hidden)/:/(auto|scroll)/,e=this.parents().filter(function(){var e=_(this);return(!n||"static"!==e.css("position"))&&i.test(e.css("overflow")+e.css("overflow-y")+e.css("overflow-x"))}).eq(0);return"fixed"!==t&&e.length?e:_(this[0].ownerDocument||document)},_.fn.extend({uniqueId:(e=0,function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++e)})}),removeUniqueId:function(){return this.each(function(){/^ui-id-\d+$/.test(this.id)&&_(this).removeAttr("id")})}})}(jQuery),function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(e){var t;return t=function(C){"use strict";if(!C.ui||!C.ui.fancytree){for(var e,h=null,c=new RegExp(/\.|\//),t=/[&<>"'/]/g,n=/[<>"'/]/g,f="$recursive_request",p="$request_target_invalid",i={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},r={16:!0,17:!0,18:!0},u={8:"backspace",9:"tab",10:"return",13:"return",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",59:";",61:"=",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scroll",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},g={16:"shift",17:"ctrl",18:"alt",91:"meta",93:"meta"},o={0:"",1:"left",2:"middle",3:"right"},y="active expanded focus folder lazy radiogroup selected unselectable unselectableIgnore".split(" "),v={},m="columns types".split(" "),b="checkbox expanded extraClasses folder icon iconTooltip key lazy partsel radiogroup refKey selected statusNodeType title tooltip type unselectable unselectableIgnore unselectableStatus".split(" "),s={},x={},a={active:!0,children:!0,data:!0,focus:!0},l=0;l<y.length;l++)v[y[l]]=!0;for(l=0;l<b.length;l++)e=b[l],s[e]=!0,e!==e.toLowerCase()&&(x[e.toLowerCase()]=e);var _=Array.isArray;return k(C.ui,"Fancytree requires jQuery UI (http://jqueryui.com)"),Date.now||(Date.now=function(){return(new Date).getTime()}),j.prototype={_findDirectChild:function(e){var t,n,i=this.children;if(i)if("string"==typeof e){for(t=0,n=i.length;t<n;t++)if(i[t].key===e)return i[t]}else{if("number"==typeof e)return this.children[e];if(e.parent===this)return e}return null},_setChildren:function(e){k(e&&(!this.children||0===this.children.length),"only init supported"),this.children=[];for(var t=0,n=e.length;t<n;t++)this.children.push(new j(this,e[t]));this.tree._callHook("treeStructureChanged",this.tree,"setChildren")},addChildren:function(e,t){var n,i,r,o,s=this.getFirstChild(),a=this.getLastChild(),l=[];for(C.isPlainObject(e)&&(e=[e]),this.children||(this.children=[]),n=0,i=e.length;n<i;n++)l.push(new j(this,e[n]));if(o=l[0],null==t?this.children=this.children.concat(l):(t=this._findDirectChild(t),k(0<=(r=C.inArray(t,this.children)),"insertBefore must be an existing child"),this.children.splice.apply(this.children,[r,0].concat(l))),s&&!t){for(n=0,i=l.length;n<i;n++)l[n].render();s!==this.getFirstChild()&&s.renderStatus(),a!==this.getLastChild()&&a.renderStatus()}else this.parent&&!this.parent.ul&&!this.tr||this.render();return 3===this.tree.options.selectMode&&this.fixSelection3FromEndNodes(),this.triggerModifyChild("add",1===l.length?l[0]:null),o},addClass:function(e){return this.toggleClass(e,!0)},addNode:function(e,t){switch(t=void 0===t||"over"===t?"child":t){case"after":return this.getParent().addChildren(e,this.getNextSibling());case"before":return this.getParent().addChildren(e,this);case"firstChild":var n=this.children?this.children[0]:null;return this.addChildren(e,n);case"child":case"over":return this.addChildren(e)}k(!1,"Invalid mode: "+t)},addPagingNode:function(e,t){var n,i;if(t=t||"child",!1!==e)return e=C.extend({title:this.tree.options.strings.moreData,statusNodeType:"paging",icon:!1},e),this.partload=!0,this.addNode(e,t);for(n=this.children.length-1;0<=n;n--)"paging"===(i=this.children[n]).statusNodeType&&this.removeChild(i);this.partload=!1},appendSibling:function(e){return this.addNode(e,"after")},applyCommand:function(e,t){return this.tree.applyCommand(e,this,t)},applyPatch:function(e){if(null===e)return this.remove(),T(this);var t,n,i={children:!0,expanded:!0,parent:!0};for(t in e)w(e,t)&&(n=e[t],i[t]||N(n)||(s[t]?this[t]=n:this.data[t]=n));return w(e,"children")&&(this.removeChildren(),e.children&&this._setChildren(e.children)),this.isVisible()&&(this.renderTitle(),this.renderStatus()),w(e,"expanded")?this.setExpanded(e.expanded):T(this)},collapseSiblings:function(){return this.tree._callHook("nodeCollapseSiblings",this)},copyTo:function(e,t,n){return e.addNode(this.toDict(!0,n),t)},countChildren:function(e){var t,n,i,r=this.children;if(!r)return 0;if(i=r.length,!1!==e)for(t=0,n=i;t<n;t++)i+=r[t].countChildren();return i},debug:function(e){4<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("log",arguments))},discard:function(){return this.warn("FancytreeNode.discard() is deprecated since 2014-02-16. Use .resetLazy() instead."),this.resetLazy()},discardMarkup:function(e){this.tree._callHook(e?"nodeRemoveMarkup":"nodeRemoveChildMarkup",this)},error:function(e){1<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("error",arguments))},findAll:function(t){t=N(t)?t:L(t);var n=[];return this.visit(function(e){t(e)&&n.push(e)}),n},findFirst:function(t){t=N(t)?t:L(t);var n=null;return this.visit(function(e){if(t(e))return n=e,!1}),n},findRelatedNode:function(e,t){return this.tree.findRelatedNode(this,e,t)},_changeSelectStatusAttrs:function(e){var t=!1,n=this.tree.options,i=h.evalOption("unselectable",this,this,n,!1),n=h.evalOption("unselectableStatus",this,this,n,void 0);switch(e=i&&null!=n?n:e){case!1:t=this.selected||this.partsel,this.selected=!1,this.partsel=!1;break;case!0:t=!this.selected||!this.partsel,this.selected=!0,this.partsel=!0;break;case void 0:t=this.selected||!this.partsel,this.selected=!1,this.partsel=!0;break;default:k(!1,"invalid state: "+e)}return t&&this.renderStatus(),t},fixSelection3AfterClick:function(e){var t=this.isSelected();this.visit(function(e){if(e._changeSelectStatusAttrs(t),e.radiogroup)return"skip"}),this.fixSelection3FromEndNodes(e)},fixSelection3FromEndNodes:function(e){var u=this.tree.options;k(3===u.selectMode,"expected selectMode 3"),function e(t){var n,i,r,o,s,a,l,d,c=t.children;if(c&&c.length){for(l=!(a=!0),n=0,i=c.length;n<i;n++)o=e(r=c[n]),h.evalOption("unselectableIgnore",r,r,u,!1)||(!1!==o&&(l=!0),!0!==o&&(a=!1));s=!!a||!!l&&void 0}else s=null==(d=h.evalOption("unselectableStatus",t,t,u,void 0))?!!t.selected:!!d;return t.partsel&&!t.selected&&t.lazy&&null==t.children&&(s=void 0),t._changeSelectStatusAttrs(s),s}(this),this.visitParents(function(e){for(var t,n,i,r=e.children,o=!0,s=!1,a=0,l=r.length;a<l;a++)t=r[a],h.evalOption("unselectableIgnore",t,t,u,!1)||(((n=null==(i=h.evalOption("unselectableStatus",t,t,u,void 0))?!!t.selected:!!i)||t.partsel)&&(s=!0),n||(o=!1));e._changeSelectStatusAttrs(n=!!o||!!s&&void 0)})},fromDict:function(e){for(var t in e)s[t]?this[t]=e[t]:"data"===t?C.extend(this.data,e.data):N(e[t])||a[t]||(this.data[t]=e[t]);e.children&&(this.removeChildren(),this.addChildren(e.children)),this.renderTitle()},getChildren:function(){if(void 0!==this.hasChildren())return this.children},getFirstChild:function(){return this.children?this.children[0]:null},getIndex:function(){return C.inArray(this,this.parent.children)},getIndexHier:function(e,n){e=e||".";var i,r=[];return C.each(this.getParentList(!1,!0),function(e,t){i=""+(t.getIndex()+1),n&&(i=("0000000"+i).substr(-n)),r.push(i)}),r.join(e)},getKeyPath:function(e){var t=this.tree.options.keyPathSeparator;return t+this.getPath(!e,"key",t)},getLastChild:function(){return this.children?this.children[this.children.length-1]:null},getLevel:function(){for(var e=0,t=this.parent;t;)e++,t=t.parent;return e},getNextSibling:function(){if(this.parent)for(var e=this.parent.children,t=0,n=e.length-1;t<n;t++)if(e[t]===this)return e[t+1];return null},getParent:function(){return this.parent},getParentList:function(e,t){for(var n=[],i=t?this:this.parent;i;)(e||i.parent)&&n.unshift(i),i=i.parent;return n},getPath:function(e,t,n){n=n||"/";var i,r=[],o=N(t=t||"title");return this.visitParents(function(e){e.parent&&(i=o?t(e):e[t],r.unshift(i))},e=!1!==e),r.join(n)},getPrevSibling:function(){if(this.parent)for(var e=this.parent.children,t=1,n=e.length;t<n;t++)if(e[t]===this)return e[t-1];return null},getSelectedNodes:function(t){var n=[];return this.visit(function(e){if(e.selected&&(n.push(e),!0===t))return"skip"}),n},hasChildren:function(){return this.lazy?null==this.children?void 0:0!==this.children.length&&(1!==this.children.length||!this.children[0].isStatusNode()||void 0):!(!this.children||!this.children.length)},hasClass:function(e){return 0<=(" "+(this.extraClasses||"")+" ").indexOf(" "+e+" ")},hasFocus:function(){return this.tree.hasFocus()&&this.tree.focusNode===this},info:function(e){3<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("info",arguments))},isActive:function(){return this.tree.activeNode===this},isBelowOf:function(e){return this.getIndexHier(".",5)>e.getIndexHier(".",5)},isChildOf:function(e){return this.parent&&this.parent===e},isDescendantOf:function(e){if(!e||e.tree!==this.tree)return!1;for(var t=this.parent;t;){if(t===e)return!0;t===t.parent&&C.error("Recursive parent link: "+t),t=t.parent}return!1},isExpanded:function(){return!!this.expanded},isFirstSibling:function(){var e=this.parent;return!e||e.children[0]===this},isFolder:function(){return!!this.folder},isLastSibling:function(){var e=this.parent;return!e||e.children[e.children.length-1]===this},isLazy:function(){return!!this.lazy},isLoaded:function(){return!this.lazy||void 0!==this.hasChildren()},isLoading:function(){return!!this._isLoading},isRoot:function(){return this.isRootNode()},isPartsel:function(){return!this.selected&&!!this.partsel},isPartload:function(){return!!this.partload},isRootNode:function(){return this.tree.rootNode===this},isSelected:function(){return!!this.selected},isStatusNode:function(){return!!this.statusNodeType},isPagingNode:function(){return"paging"===this.statusNodeType},isTopLevel:function(){return this.tree.rootNode===this.parent},isUndefined:function(){return void 0===this.hasChildren()},isVisible:function(){var e,t,n=this.tree.enableFilter,i=this.getParentList(!1,!1);if(n&&!this.match&&!this.subMatchCount)return!1;for(e=0,t=i.length;e<t;e++)if(!i[e].expanded)return!1;return!0},lazyLoad:function(e){C.error("FancytreeNode.lazyLoad() is deprecated since 2014-02-16. Use .load() instead.")},load:function(e){var t=this,n=this.isExpanded();return k(this.isLazy(),"load() requires a lazy node"),e||this.isUndefined()?(this.isLoaded()&&this.resetLazy(),!1===(e=this.tree._triggerNodeEvent("lazyLoad",this))?T(this):(k("boolean"!=typeof e,"lazyLoad event must return source in data.result"),e=this.tree._callHook("nodeLoadChildren",this,e),n?(this.expanded=!0,e.always(function(){t.render()})):e.always(function(){t.renderStatus()}),e)):T(this)},makeVisible:function(e){for(var t=this,n=[],i=new C.Deferred,r=this.getParentList(!1,!1),o=r.length,s=!(e&&!0===e.noAnimation),a=!(e&&!1===e.scrollIntoView),l=o-1;0<=l;l--)n.push(r[l].setExpanded(!0,e));return C.when.apply(C,n).done(function(){a?t.scrollIntoView(s).done(function(){i.resolve()}):i.resolve()}),i.promise()},moveTo:function(t,e,n){void 0===e||"over"===e?e="child":"firstChild"===e&&(t.children&&t.children.length?(e="before",t=t.children[0]):e="child");var i,r=this.tree,o=this.parent,s="child"===e?t:t.parent;if(this!==t){if(this.parent?s.isDescendantOf(this)&&C.error("Cannot move a node to its own descendant"):C.error("Cannot move system root"),s!==o&&o.triggerModifyChild("remove",this),1===this.parent.children.length){if(this.parent===s)return;this.parent.children=this.parent.lazy?[]:null,this.parent.expanded=!1}else k(0<=(i=C.inArray(this,this.parent.children)),"invalid source parent"),this.parent.children.splice(i,1);if((this.parent=s).hasChildren())switch(e){case"child":s.children.push(this);break;case"before":k(0<=(i=C.inArray(t,s.children)),"invalid target parent"),s.children.splice(i,0,this);break;case"after":k(0<=(i=C.inArray(t,s.children)),"invalid target parent"),s.children.splice(i+1,0,this);break;default:C.error("Invalid mode "+e)}else s.children=[this];n&&t.visit(n,!0),s===o?s.triggerModifyChild("move",this):s.triggerModifyChild("add",this),r!==t.tree&&(this.warn("Cross-tree moveTo is experimental!"),this.visit(function(e){e.tree=t.tree},!0)),r._callHook("treeStructureChanged",r,"moveTo"),o.isDescendantOf(s)||o.render(),s.isDescendantOf(o)||s===o||s.render()}},navigate:function(e,t){var n=C.ui.keyCode;switch(e){case"left":case n.LEFT:if(this.expanded)return this.setExpanded(!1);break;case"right":case n.RIGHT:if(!this.expanded&&(this.children||this.lazy))return this.setExpanded()}if(n=this.findRelatedNode(e)){try{n.makeVisible({scrollIntoView:!1})}catch(e){}return!1===t?(n.setFocus(),T()):n.setActive()}return this.warn("Could not find related node '"+e+"'."),T()},remove:function(){return this.parent.removeChild(this)},removeChild:function(e){return this.tree._callHook("nodeRemoveChild",this,e)},removeChildren:function(){return this.tree._callHook("nodeRemoveChildren",this)},removeClass:function(e){return this.toggleClass(e,!1)},render:function(e,t){return this.tree._callHook("nodeRender",this,e,t)},renderTitle:function(){return this.tree._callHook("nodeRenderTitle",this)},renderStatus:function(){return this.tree._callHook("nodeRenderStatus",this)},replaceWith:function(e){var n=this.parent,i=C.inArray(this,n.children),r=this;return k(this.isPagingNode(),"replaceWith() currently requires a paging status node"),(e=this.tree._callHook("nodeLoadChildren",this,e)).done(function(e){var t=r.children;for(l=0;l<t.length;l++)t[l].parent=n;n.children.splice.apply(n.children,[i+1,0].concat(t)),r.children=null,r.remove(),n.render()}).fail(function(){r.setExpanded()}),e},resetLazy:function(){this.removeChildren(),this.expanded=!1,this.lazy=!0,this.children=void 0,this.renderStatus()},scheduleAction:function(e,t){this.tree.timer&&(clearTimeout(this.tree.timer),this.tree.debug("clearTimeout(%o)",this.tree.timer)),this.tree.timer=null;var n=this;switch(e){case"cancel":break;case"expand":this.tree.timer=setTimeout(function(){n.tree.debug("setTimeout: trigger expand"),n.setExpanded(!0)},t);break;case"activate":this.tree.timer=setTimeout(function(){n.tree.debug("setTimeout: trigger activate"),n.setActive(!0)},t);break;default:C.error("Invalid mode "+e)}},scrollIntoView:function(e,t){if(void 0!==t&&((p=t).tree&&void 0!==p.statusNodeType))throw Error("scrollIntoView() with 'topNode' option is deprecated since 2014-05-08. Use 'options.topNode' instead.");var n=C.extend({effects:!0===e?{duration:200,queue:!1}:e,scrollOfs:this.tree.options.scrollOfs,scrollParent:this.tree.options.scrollParent,topNode:null},t),i=n.scrollParent,r=this.tree.$container,o=r.css("overflow-y");i?i.jquery||(i=C(i)):i=!this.tree.tbody&&("scroll"===o||"auto"===o)?r:r.scrollParent(),i[0]!==document&&i[0]!==document.body||(this.debug("scrollIntoView(): normalizing scrollParent to 'window':",i[0]),i=C(window));var s,a,l=new C.Deferred,d=this,c=C(this.span).height(),u=n.scrollOfs.top||0,h=n.scrollOfs.bottom||0,f=i.height(),p=i.scrollTop(),e=i,t=i[0]===window,o=n.topNode||null,r=null;return this.isRootNode()||!this.isVisible()?(this.info("scrollIntoView(): node is invisible."),T()):(t?(a=C(this.span).offset().top,s=o&&o.span?C(o.span).offset().top:0,e=C("html,body")):(k(i[0]!==document&&i[0]!==document.body,"scrollParent should be a simple element or `window`, not document or body."),t=i.offset().top,a=C(this.span).offset().top-t+p,s=o?C(o.span).offset().top-t+p:0,f-=Math.max(0,i.innerHeight()-i[0].clientHeight)),a<p+u?r=a-u:p+f-h<a+c&&(r=a+c-f+h,o&&(k(o.isRootNode()||o.isVisible(),"topNode must be visible"),s<r&&(r=s-u))),null===r?l.resolveWith(this):n.effects?(n.effects.complete=function(){l.resolveWith(d)},e.stop(!0).animate({scrollTop:r},n.effects)):(e[0].scrollTop=r,l.resolveWith(this)),l.promise())},setActive:function(e,t){return this.tree._callHook("nodeSetActive",this,e,t)},setExpanded:function(e,t){return this.tree._callHook("nodeSetExpanded",this,e,t)},setFocus:function(e){return this.tree._callHook("nodeSetFocus",this,e)},setSelected:function(e,t){return this.tree._callHook("nodeSetSelected",this,e,t)},setStatus:function(e,t,n){return this.tree._callHook("nodeSetStatus",this,e,t,n)},setTitle:function(e){this.title=e,this.renderTitle(),this.triggerModify("rename")},sortChildren:function(e,t){var n,i,r=this.children;if(r){if(r.sort(e=e||function(e,t){e=e.title.toLowerCase(),t=t.title.toLowerCase();return e===t?0:t<e?1:-1}),t)for(n=0,i=r.length;n<i;n++)r[n].children&&r[n].sortChildren(e,"$norender$");"$norender$"!==t&&this.render(),this.triggerModifyChild("sort")}},toDict:function(e,t){var n,i,r,o,s={},a=this;if(C.each(b,function(e,t){!a[t]&&!1!==a[t]||(s[t]=a[t])}),C.isEmptyObject(this.data)||(s.data=C.extend({},this.data),C.isEmptyObject(s.data)&&delete s.data),t){if(!1===(o=t(s,a)))return!1;"skip"===o&&(e=!1)}if(e&&_(this.children))for(s.children=[],n=0,i=this.children.length;n<i;n++)(r=this.children[n]).isStatusNode()||!1!==(o=r.toDict(!0,t))&&s.children.push(o);return s},toggleClass:function(e,t){var n,i,r=e.match(/\S+/g)||[],o=0,s=!1,a=this[this.tree.statusClassPropName],l=" "+(this.extraClasses||"")+" ";for(a&&C(a).toggleClass(e,t);n=r[o++];)if(i=0<=l.indexOf(" "+n+" "),t=void 0===t?!i:!!t)i||(l+=n+" ",s=!0);else for(;-1<l.indexOf(" "+n+" ");)l=l.replace(" "+n+" "," ");return this.extraClasses=S(l),s},toggleExpanded:function(){return this.tree._callHook("nodeToggleExpanded",this)},toggleSelected:function(){return this.tree._callHook("nodeToggleSelected",this)},toString:function(){return"FancytreeNode@"+this.key+"[title='"+this.title+"']"},triggerModifyChild:function(e,t,n){var i=this.tree.options.modifyChild;i&&(t&&t.parent!==this&&C.error("childNode "+t+" is not a child of "+this),t={node:this,tree:this.tree,operation:e,childNode:t||null},n&&C.extend(t,n),i({type:"modifyChild"},t))},triggerModify:function(e,t){this.parent.triggerModifyChild(e,this,t)},visit:function(e,t){var n,i,r=!0,o=this.children;if(!0===t&&(!1===(r=e(this))||"skip"===r))return r;if(o)for(n=0,i=o.length;n<i&&!1!==(r=o[n].visit(e,!0));n++);return r},visitAndLoad:function(n,e,t){var i,r,o,s=this;return!n||!0!==e||!1!==(r=n(s))&&"skip"!==r?s.children||s.lazy?(i=new C.Deferred,o=[],s.load().done(function(){for(var e=0,t=s.children.length;e<t;e++){if(!1===(r=s.children[e].visitAndLoad(n,!0,!0))){i.reject();break}"skip"!==r&&o.push(r)}C.when.apply(this,o).then(function(){i.resolve()})}),i.promise()):T():t?r:T()},visitParents:function(e,t){if(t&&!1===e(this))return!1;for(var n=this.parent;n;){if(!1===e(n))return!1;n=n.parent}return!0},visitSiblings:function(e,t){for(var n,i=this.parent.children,r=0,o=i.length;r<o;r++)if(n=i[r],(t||n!==this)&&!1===e(n))return!1;return!0},warn:function(e){2<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("warn",arguments))}},F.prototype={_makeHookContext:function(e,t,n){var i,r;return void 0!==e.node?(t&&e.originalEvent!==t&&C.error("invalid args"),i=e):e.tree?i={node:e,tree:r=e.tree,widget:r.widget,options:r.widget.options,originalEvent:t,typeInfo:r.types[e.type]||{}}:e.widget?i={node:null,tree:e,widget:e.widget,options:e.widget.options,originalEvent:t}:C.error("invalid args"),n&&C.extend(i,n),i},_callHook:function(e,t,n){var i=this._makeHookContext(t),r=this[e],t=Array.prototype.slice.call(arguments,2);return N(r)||C.error("_callHook('"+e+"') is not a function"),t.unshift(i),r.apply(this,t)},_setExpiringValue:function(e,t,n){this._tempCache[e]={value:t,expire:Date.now()+(+n||50)}},_getExpiringValue:function(e){var t=this._tempCache[e];return t&&t.expire>Date.now()?t.value:(delete this._tempCache[e],null)},_usesExtension:function(e){return 0<=C.inArray(e,this.options.extensions)},_requireExtension:function(e,t,n,i){null!=n&&(n=!!n);var r=this._local.name,o=this.options.extensions,s=C.inArray(e,o)<C.inArray(r,o),o=t&&null==this.ext[e],s=!o&&null!=n&&n!==s;return k(r&&r!==e,"invalid or same name '"+r+"' (require yourself?)"),!o&&!s||(i||(o||t?(i="'"+r+"' extension requires '"+e+"'",s&&(i+=" to be registered "+(n?"before":"after")+" itself")):i="If used together, `"+e+"` must be registered "+(n?"before":"after")+" `"+r+"`"),C.error(i),!1)},activateKey:function(e,t){e=this.getNodeByKey(e);return e?e.setActive(!0,t):this.activeNode&&this.activeNode.setActive(!1,t),e},addPagingNode:function(e,t){return this.rootNode.addPagingNode(e,t)},applyCommand:function(e,t,n){var i;switch(t=t||this.getActiveNode(),e){case"moveUp":(i=t.getPrevSibling())&&(t.moveTo(i,"before"),t.setActive());break;case"moveDown":(i=t.getNextSibling())&&(t.moveTo(i,"after"),t.setActive());break;case"indent":(i=t.getPrevSibling())&&(t.moveTo(i,"child"),i.setExpanded(),t.setActive());break;case"outdent":t.isTopLevel()||(t.moveTo(t.getParent(),"after"),t.setActive());break;case"remove":i=t.getPrevSibling()||t.getParent(),t.remove(),i&&i.setActive();break;case"addChild":t.editCreateNode("child","");break;case"addSibling":t.editCreateNode("after","");break;case"rename":t.editStart();break;case"down":case"first":case"last":case"left":case"parent":case"right":case"up":return t.navigate(e);default:C.error("Unhandled command: '"+e+"'")}},applyPatch:function(e){for(var t,n,i,r,o=e.length,s=[],a=0;a<o;a++)k(2===(t=e[a]).length,"patchList must be an array of length-2-arrays"),n=t[0],i=t[1],(r=null===n?this.rootNode:this.getNodeByKey(n))?(t=new C.Deferred,s.push(t),r.applyPatch(i).always(O(t,r))):this.warn("could not find node with key '"+n+"'");return C.when.apply(C,s).promise()},clear:function(e){this._callHook("treeClear",this)},count:function(){return this.rootNode.countChildren()},debug:function(e){4<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("log",arguments))},destroy:function(){this.widget.destroy()},enable:function(e){!1===e?this.widget.disable():this.widget.enable()},enableUpdate:function(e){return!!this._enableUpdate==!!(e=!1!==e)?e:((this._enableUpdate=e)?(this.debug("enableUpdate(true): redraw "),this._callHook("treeStructureChanged",this,"enableUpdate"),this.render()):this.debug("enableUpdate(false)..."),!e)},error:function(e){1<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("error",arguments))},expandAll:function(t,n){var e=this.enableUpdate(!1);t=!1!==t,this.visit(function(e){!1!==e.hasChildren()&&e.isExpanded()!==t&&e.setExpanded(t,n)}),this.enableUpdate(e)},findAll:function(e){return this.rootNode.findAll(e)},findFirst:function(e){return this.rootNode.findFirst(e)},findNextNode:function(t,n){var i,r=null,e=this.getFirstChild();function o(e){if((r=t(e)?e:r)||e===n)return!1}return t="string"==typeof t?(i=new RegExp("^"+t,"i"),function(e){return i.test(e.title)}):t,this.visitRows(o,{start:n=n||e,includeSelf:!1}),r||n===e||this.visitRows(o,{start:e,includeSelf:!0}),r},findRelatedNode:function(e,t,n){var i=null,r=C.ui.keyCode;switch(t){case"parent":case r.BACKSPACE:e.parent&&e.parent.parent&&(i=e.parent);break;case"first":case r.HOME:this.visit(function(e){if(e.isVisible())return i=e,!1});break;case"last":case r.END:this.visit(function(e){e.isVisible()&&(i=e)});break;case"left":case r.LEFT:e.expanded?e.setExpanded(!1):e.parent&&e.parent.parent&&(i=e.parent);break;case"right":case r.RIGHT:e.expanded||!e.children&&!e.lazy?e.children&&e.children.length&&(i=e.children[0]):(e.setExpanded(),i=e);break;case"up":case r.UP:this.visitRows(function(e){return i=e,!1},{start:e,reverse:!0,includeSelf:!1});break;case"down":case r.DOWN:this.visitRows(function(e){return i=e,!1},{start:e,includeSelf:!1});break;default:this.tree.warn("Unknown relation '"+t+"'.")}return i},generateFormElements:function(e,t,n){n=n||{};var i="string"==typeof e?e:"ft_"+this._id+"[]",r="string"==typeof t?t:"ft_"+this._id+"_active",o="fancytree_result_"+this._id,s=C("#"+o),a=3===this.options.selectMode&&!1!==n.stopOnParents;function l(e){s.append(C("<input>",{type:"checkbox",name:i,value:e.key,checked:!0}))}s.length?s.empty():s=C("<div>",{id:o}).hide().insertAfter(this.$container),!1!==t&&this.activeNode&&s.append(C("<input>",{type:"radio",name:r,value:this.activeNode.key,checked:!0})),n.filter?this.visit(function(e){var t=n.filter(e);if("skip"===t)return t;!1!==t&&l(e)}):!1!==e&&(a=this.getSelectedNodes(a),C.each(a,function(e,t){l(t)}))},getActiveNode:function(){return this.activeNode},getFirstChild:function(){return this.rootNode.getFirstChild()},getFocusNode:function(){return this.focusNode},getOption:function(e){return this.widget.option(e)},getNodeByKey:function(t,e){var n,i;return!e&&(n=document.getElementById(this.options.idPrefix+t))?n.ftnode||null:(e=e||this.rootNode,t=""+t,e.visit(function(e){if(e.key===t)return i=e,!1},!(i=null)),i)},getRootNode:function(){return this.rootNode},getSelectedNodes:function(e){return this.rootNode.getSelectedNodes(e)},hasFocus:function(){return!!this._hasFocus},info:function(e){3<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("info",arguments))},isLoading:function(){var t=!1;return this.rootNode.visit(function(e){if(e._isLoading||e._requestId)return!(t=!0)},!0),t},loadKeyPath:function(e,t){var i,n,r,o=this,s=new C.Deferred,a=this.getRootNode(),l=this.options.keyPathSeparator,d=[],c=C.extend({},t);for("function"==typeof t?i=t:t&&t.callback&&(i=t.callback),c.callback=function(e,t,n){i&&i.call(e,t,n),s.notifyWith(e,[{node:t,status:n}])},null==c.matchKey&&(c.matchKey=function(e,t){return e.key===t}),_(e)||(e=[e]),n=0;n<e.length;n++)(r=e[n]).charAt(0)===l&&(r=r.substr(1)),d.push(r.split(l));return setTimeout(function(){o._loadKeyPathImpl(s,c,a,d).done(function(){s.resolve()})},0),s.promise()},_loadKeyPathImpl:function(e,o,t,n){var i,r,s,a,l,d,c,u,h,f,p=this;for(c={},r=0;r<n.length;r++)for(h=n[r],u=t;h.length;){if(s=h.shift(),!(a=function(e,t){var n,i,r=e.children;if(r)for(n=0,i=r.length;n<i;n++)if(o.matchKey(r[n],t))return r[n];return null}(u,s))){this.warn("loadKeyPath: key not found: "+s+" (parent: "+u+")"),o.callback(this,s,"error");break}if(0===h.length){o.callback(this,a,"ok");break}if(a.lazy&&void 0===a.hasChildren()){o.callback(this,a,"loaded"),c[s=a.key]?c[s].pathSegList.push(h):c[s]={parent:a,pathSegList:[h]};break}o.callback(this,a,"loaded"),u=a}for(l in i=[],c)w(c,l)&&(d=c[l],f=new C.Deferred,i.push(f),function(t,n,e){o.callback(p,n,"loading"),n.load().done(function(){p._loadKeyPathImpl.call(p,t,o,n,e).always(O(t,p))}).fail(function(e){p.warn("loadKeyPath: error loading lazy "+n),o.callback(p,a,"error"),t.rejectWith(p)})}(f,d.parent,d.pathSegList));return C.when.apply(C,i).promise()},reactivate:function(e){var t,n=this.activeNode;return n?(this.activeNode=null,t=n.setActive(!0,{noFocus:!0}),e&&n.setFocus(),t):T()},reload:function(e){return this._callHook("treeClear",this),this._callHook("treeLoad",this,e)},render:function(e,t){return this.rootNode.render(e,t)},selectAll:function(t){this.visit(function(e){e.setSelected(t)})},setFocus:function(e){return this._callHook("treeSetFocus",this,e)},setOption:function(e,t){return this.widget.option(e,t)},debugTime:function(e){4<=this.options.debugLevel&&window.console.time(this+" - "+e)},debugTimeEnd:function(e){4<=this.options.debugLevel&&window.console.timeEnd(this+" - "+e)},toDict:function(e,t){t=this.rootNode.toDict(!0,t);return e?t:t.children},toString:function(){return"Fancytree@"+this._id},_triggerNodeEvent:function(e,t,n,i){i=this._makeHookContext(t,n,i),n=this.widget._trigger(e,n,i);return!1!==n&&void 0!==i.result?i.result:n},_triggerTreeEvent:function(e,t,n){n=this._makeHookContext(this,t,n),t=this.widget._trigger(e,t,n);return!1!==t&&void 0!==n.result?n.result:t},visit:function(e){return this.rootNode.visit(e,!1)},visitRows:function(t,e){if(!this.rootNode.hasChildren())return!1;if(e&&e.reverse)return delete e.reverse,this._visitRowsUp(t,e);for(var n,i,r,o=0,s=!1===(e=e||{}).includeSelf,a=!!e.includeHidden,l=!a&&this.enableFilter,d=e.start||this.rootNode.children[0],c=d.parent;c;){for(k(0<=(i=(r=c.children).indexOf(d)+o),"Could not find "+d+" in parent's children: "+c),n=i;n<r.length;n++)if(d=r[n],!l||d.match||d.subMatchCount){if(!s&&!1===t(d))return!1;if(s=!1,d.children&&d.children.length&&(a||d.expanded)&&!1===d.visit(function(e){return!l||e.match||e.subMatchCount?!1!==t(e)&&(a||!e.children||e.expanded?void 0:"skip"):"skip"},!1))return!1}c=(d=c).parent,o=1}return!0},_visitRowsUp:function(e,t){for(var n,i,r,o=!!t.includeHidden,s=t.start||this.rootNode.children[0];;){if((n=(r=s.parent).children)[0]===s){if(!(s=r).parent)break;n=r.children}else for(i=n.indexOf(s),s=n[i-1];(o||s.expanded)&&s.children&&s.children.length;)s=(n=(r=s).children)[n.length-1];if((o||s.isVisible())&&!1===e(s))return!1}},warn:function(e){2<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("warn",arguments))}},C.extend(F.prototype,{nodeClick:function(e){var t,n,i=e.targetType,r=e.node;if("expander"===i)r.isLoading()?r.debug("Got 2nd click while loading: ignored"):this._callHook("nodeToggleExpanded",e);else if("checkbox"===i)this._callHook("nodeToggleSelected",e),e.options.focusOnSelect&&this._callHook("nodeSetFocus",e,!0);else{if(t=!(n=!1),r.folder)switch(e.options.clickFolderMode){case 2:t=!(n=!0);break;case 3:n=t=!0}t&&(this.nodeSetFocus(e),this._callHook("nodeSetActive",e,!0)),n&&this._callHook("nodeToggleExpanded",e)}},nodeCollapseSiblings:function(e,t){var n,i,r,o=e.node;if(o.parent)for(i=0,r=(n=o.parent.children).length;i<r;i++)n[i]!==o&&n[i].expanded&&this._callHook("nodeSetExpanded",n[i],!1,t)},nodeDblclick:function(e){"title"===e.targetType&&4===e.options.clickFolderMode&&this._callHook("nodeToggleExpanded",e),"title"===e.targetType&&e.originalEvent.preventDefault()},nodeKeydown:function(e){var t=e.originalEvent,n=e.node,i=e.tree,r=e.options,o=t.which,s=t.key||String.fromCharCode(o),a=!!(t.altKey||t.ctrlKey||t.metaKey),l=!g[o]&&!u[o]&&!a,o=C(t.target),d=!0,c=!(t.ctrlKey||!r.autoActivate);if(n||(a=this.getActiveNode()||this.getFirstChild())&&(a.setFocus(),(n=e.node=this.focusNode).debug("Keydown force focus on active node")),r.quicksearch&&l&&!o.is(":input:enabled"))return 500<(o=Date.now())-i.lastQuicksearchTime&&(i.lastQuicksearchTerm=""),i.lastQuicksearchTime=o,i.lastQuicksearchTerm+=s,(s=i.findNextNode(i.lastQuicksearchTerm,i.getActiveNode()))&&s.setActive(),void t.preventDefault();switch(h.eventToString(t)){case"+":case"=":i.nodeSetExpanded(e,!0);break;case"-":i.nodeSetExpanded(e,!1);break;case"space":n.isPagingNode()?i._triggerNodeEvent("clickPaging",e,t):h.evalOption("checkbox",n,n,r,!1)?i.nodeToggleSelected(e):i.nodeSetActive(e,!0);break;case"return":i.nodeSetActive(e,!0);break;case"home":case"end":case"backspace":case"left":case"right":case"up":case"down":n.navigate(t.which,c);break;default:d=!1}d&&t.preventDefault()},nodeLoadChildren:function(o,s){var t,n,a,e=null,i=!0,l=o.tree,d=o.node,c=d.parent,r="nodeLoadChildren",u=Date.now();return N(s)&&k(!N(s=s.call(l,{type:"source"},o)),"source callback must not return another function"),N(s.then)?e=s:s.url?e=(t=C.extend({},o.options.ajax,s)).debugDelay?(n=t.debugDelay,delete t.debugDelay,_(n)&&(n=n[0]+Math.random()*(n[1]-n[0])),d.warn("nodeLoadChildren waiting debugDelay "+Math.round(n)+" ms ..."),C.Deferred(function(e){setTimeout(function(){C.ajax(t).done(function(){e.resolveWith(this,arguments)}).fail(function(){e.rejectWith(this,arguments)})},n)})):C.ajax(t):C.isPlainObject(s)||_(s)?i=!(e={then:function(e,t){e(s,null,null)}}):C.error("Invalid source type: "+s),d._requestId&&(d.warn("Recursive load request #"+u+" while #"+d._requestId+" is pending."),d._requestId=u),i&&(l.debugTime(r),l.nodeSetStatus(o,"loading")),a=new C.Deferred,e.then(function(e,t,n){var i,r;if("json"!==s.dataType&&"jsonp"!==s.dataType||"string"!=typeof e||C.error("Ajax request returned a string (did you get the JSON dataType wrong?)."),d._requestId&&d._requestId>u)a.rejectWith(this,[f]);else if(null!==d.parent||null===c){if(o.options.postProcess){try{(r=l._triggerNodeEvent("postProcess",o,o.originalEvent,{response:e,error:null,dataType:s.dataType})).error&&l.warn("postProcess returned error:",r)}catch(e){r={error:e,message:""+e,details:"postProcess failed"}}if(r.error)return i=C.isPlainObject(r.error)?r.error:{message:r.error},i=l._makeHookContext(d,null,i),void a.rejectWith(this,[i]);(_(r)||C.isPlainObject(r)&&_(r.children))&&(e=r)}else e&&w(e,"d")&&o.options.enableAspx&&(42===o.options.enableAspx&&l.warn("The default for enableAspx will change to `false` in the fututure. Pass `enableAspx: true` or implement postProcess to silence this warning."),e="string"==typeof e.d?C.parseJSON(e.d):e.d);a.resolveWith(this,[e])}else a.rejectWith(this,[p])},function(e,t,n){n=l._makeHookContext(d,null,{error:e,args:Array.prototype.slice.call(arguments),message:n,details:e.status+": "+n});a.rejectWith(this,[n])}),a.done(function(e){var t,n,i;l.nodeSetStatus(o,"ok"),C.isPlainObject(e)?(k(d.isRootNode(),"source may only be an object for root nodes (expecting an array of child objects otherwise)"),k(_(e.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),t=(n=e).children,delete n.children,C.each(m,function(e,t){void 0!==n[t]&&(l[t]=n[t],delete n[t])}),C.extend(l.data,n)):t=e,k(_(t),"expected array of children"),d._setChildren(t),l.options.nodata&&0===t.length&&(N(l.options.nodata)?i=l.options.nodata.call(l,{type:"nodata"},o):!0===l.options.nodata&&d.isRootNode()?i=l.options.strings.noData:"string"==typeof l.options.nodata&&d.isRootNode()&&(i=l.options.nodata),i&&d.setStatus("nodata",i)),l._triggerNodeEvent("loadChildren",d)}).fail(function(e){var t;e!==f?e!==p?(e.node&&e.error&&e.message?t=e:"[object Object]"===(t=l._makeHookContext(d,null,{error:e,args:Array.prototype.slice.call(arguments),message:e?e.message||e.toString():""})).message&&(t.message=""),d.warn("Load children failed ("+t.message+")",t),!1!==l._triggerNodeEvent("loadError",t,null)&&l.nodeSetStatus(o,"error",t.message,t.details)):d.warn("Lazy parent node was removed while loading: discarding response."):d.warn("Ignored response for obsolete load request #"+u+" (expected #"+d._requestId+")")}).always(function(){d._requestId=null,i&&l.debugTimeEnd(r)}),a.promise()},nodeLoadKeyPath:function(e,t){},nodeRemoveChild:function(e,t){var n=e.node,i=C.extend({},e,{node:t}),r=n.children;if(1===r.length)return k(t===r[0],"invalid single child"),this.nodeRemoveChildren(e);this.activeNode&&(t===this.activeNode||this.activeNode.isDescendantOf(t))&&this.activeNode.setActive(!1),this.focusNode&&(t===this.focusNode||this.focusNode.isDescendantOf(t))&&(this.focusNode=null),this.nodeRemoveMarkup(i),this.nodeRemoveChildren(i),k(0<=(i=C.inArray(t,r)),"invalid child"),n.triggerModifyChild("remove",t),t.visit(function(e){e.parent=null},!0),this._callHook("treeRegisterNode",this,!1,t),r.splice(i,1)},nodeRemoveChildMarkup:function(e){e=e.node;e.ul&&(e.isRootNode()?C(e.ul).empty():(C(e.ul).remove(),e.ul=null),e.visit(function(e){e.li=e.ul=null}))},nodeRemoveChildren:function(e){var t=e.tree,n=e.node;n.children&&(this.activeNode&&this.activeNode.isDescendantOf(n)&&this.activeNode.setActive(!1),this.focusNode&&this.focusNode.isDescendantOf(n)&&(this.focusNode=null),this.nodeRemoveChildMarkup(e),n.triggerModifyChild("remove",null),n.visit(function(e){e.parent=null,t._callHook("treeRegisterNode",t,!1,e)}),n.lazy?n.children=[]:n.children=null,n.isRootNode()||(n.expanded=!1),this.nodeRenderStatus(e))},nodeRemoveMarkup:function(e){var t=e.node;t.li&&(C(t.li).remove(),t.li=null),this.nodeRemoveChildMarkup(e)},nodeRender:function(e,t,n,i,r){var o,s,a,l,d,c,u,h=e.node,f=e.tree,p=e.options,g=p.aria,y=!1,v=h.parent,m=!v,b=h.children,x=null;if(!1!==f._enableUpdate&&(m||v.ul)){if(k(m||v.ul,"parent UL must exist"),m||(h.li&&(t||h.li.parentNode!==h.parent.ul)&&(h.li.parentNode===h.parent.ul?x=h.li.nextSibling:this.debug("Unlinking "+h+" (must be child of "+h.parent+")"),this.nodeRemoveMarkup(e)),h.li?this.nodeRenderStatus(e):(y=!0,h.li=document.createElement("li"),(h.li.ftnode=h).key&&p.generateIds&&(h.li.id=p.idPrefix+h.key),h.span=document.createElement("span"),h.span.className="fancytree-node",g&&!h.tr&&C(h.li).attr("role","treeitem"),h.li.appendChild(h.span),this.nodeRenderTitle(e),p.createNode&&p.createNode.call(f,{type:"createNode"},e)),p.renderNode&&p.renderNode.call(f,{type:"renderNode"},e)),b){if(m||h.expanded||!0===n){for(h.ul||(h.ul=document.createElement("ul"),(!0!==i||r)&&h.expanded||(h.ul.style.display="none"),g&&C(h.ul).attr("role","group"),h.li?h.li.appendChild(h.ul):h.tree.$div.append(h.ul)),l=0,d=b.length;l<d;l++)u=C.extend({},e,{node:b[l]}),this.nodeRender(u,t,n,!1,!0);for(o=h.ul.firstChild;o;)o=(a=o.ftnode)&&a.parent!==h?(h.debug("_fixParent: remove missing "+a,o),c=o.nextSibling,o.parentNode.removeChild(o),c):o.nextSibling;for(o=h.ul.firstChild,l=0,d=b.length-1;l<d;l++)(s=b[l])===(a=o.ftnode)?o=o.nextSibling:h.ul.insertBefore(s.li,a.li)}}else h.ul&&(this.warn("remove child markup for "+h),this.nodeRemoveChildMarkup(e));m||y&&v.ul.insertBefore(h.li,x)}},nodeRenderTitle:function(e,t){var n,i,r=e.node,o=e.tree,s=e.options,a=s.aria,l=r.getLevel(),d=[];void 0!==t&&(r.title=t),r.span&&!1!==o._enableUpdate&&(t=a&&!1!==r.hasChildren()?" role='button'":"",l<s.minExpandLevel?(r.lazy||(r.expanded=!0),1<l&&d.push("<span "+t+" class='fancytree-expander fancytree-expander-fixed'></span>")):d.push("<span "+t+" class='fancytree-expander'></span>"),(l=h.evalOption("checkbox",r,r,s,!1))&&!r.isStatusNode()&&(n="fancytree-checkbox",("radio"===l||r.parent&&r.parent.radiogroup)&&(n+=" fancytree-radio"),d.push("<span "+(t=a?" role='checkbox'":"")+" class='"+n+"'></span>")),void 0!==r.data.iconClass&&(r.icon?C.error("'iconClass' node option is deprecated since v2.14.0: use 'icon' only instead"):(r.warn("'iconClass' node option is deprecated since v2.14.0: use 'icon' instead"),r.icon=r.data.iconClass)),!1!==(n=h.evalOption("icon",r,r,s,!0))&&(t=a?" role='presentation'":"",i=(i=h.evalOption("iconTooltip",r,r,s,null))?" title='"+D(i)+"'":"","string"==typeof n?c.test(n)?(n="/"===n.charAt(0)?n:(s.imagePath||"")+n,d.push("<img src='"+n+"' class='fancytree-icon'"+i+" alt='' />")):d.push("<span "+t+" class='fancytree-custom-icon "+n+"'"+i+"></span>"):n.text?d.push("<span "+t+" class='fancytree-custom-icon "+(n.addClass||"")+"'"+i+">"+h.escapeHtml(n.text)+"</span>"):n.html?d.push("<span "+t+" class='fancytree-custom-icon "+(n.addClass||"")+"'"+i+">"+n.html+"</span>"):d.push("<span "+t+" class='fancytree-icon'"+i+"></span>")),t="",t=(t=s.renderTitle?s.renderTitle.call(o,{type:"renderTitle"},e)||"":t)||"<span class='fancytree-title'"+(i=(i=!0===(i=h.evalOption("tooltip",r,r,s,null))?r.title:i)?" title='"+D(i)+"'":"")+(s.titlesTabbable?" tabindex='0'":"")+">"+(s.escapeTitles?h.escapeHtml(r.title):r.title)+"</span>",d.push(t),r.span.innerHTML=d.join(""),this.nodeRenderStatus(e),s.enhanceTitle&&(e.$title=C(">span.fancytree-title",r.span),t=s.enhanceTitle.call(o,{type:"enhanceTitle"},e)||""))},nodeRenderStatus:function(e){var t,n=e.node,i=e.tree,r=e.options,o=n.hasChildren(),s=n.isLastSibling(),a=r.aria,l=r._classNames,d=[],e=n[i.statusClassPropName];e&&!1!==i._enableUpdate&&(a&&(t=C(n.tr||n.li)),d.push(l.node),i.activeNode===n&&d.push(l.active),i.focusNode===n&&d.push(l.focused),n.expanded&&d.push(l.expanded),a&&(!1===o?t.removeAttr("aria-expanded"):t.attr("aria-expanded",Boolean(n.expanded))),n.folder&&d.push(l.folder),!1!==o&&d.push(l.hasChildren),s&&d.push(l.lastsib),n.lazy&&null==n.children&&d.push(l.lazy),n.partload&&d.push(l.partload),n.partsel&&d.push(l.partsel),h.evalOption("unselectable",n,n,r,!1)&&d.push(l.unselectable),n._isLoading&&d.push(l.loading),n._error&&d.push(l.error),n.statusNodeType&&d.push(l.statusNodePrefix+n.statusNodeType),n.selected?(d.push(l.selected),a&&t.attr("aria-selected",!0)):a&&t.attr("aria-selected",!1),n.extraClasses&&d.push(n.extraClasses),!1===o?d.push(l.combinedExpanderPrefix+"n"+(s?"l":"")):d.push(l.combinedExpanderPrefix+(n.expanded?"e":"c")+(n.lazy&&null==n.children?"d":"")+(s?"l":"")),d.push(l.combinedIconPrefix+(n.expanded?"e":"c")+(n.folder?"f":"")),e.className=d.join(" "),n.li&&C(n.li).toggleClass(l.lastsib,s))},nodeSetActive:function(e,t,n){var i=e.node,r=e.tree,o=e.options,s=!0===(n=n||{}).noEvents,a=!0===n.noFocus,n=!1!==n.scrollIntoView;return i===r.activeNode===(t=!1!==t)?T(i):(n&&e.originalEvent&&C(e.originalEvent.target).is("a,:checkbox")&&(i.info("Not scrolling while clicking an embedded link."),n=!1),t&&!s&&!1===this._triggerNodeEvent("beforeActivate",i,e.originalEvent)?A(i,["rejected"]):(t?(r.activeNode&&(k(r.activeNode!==i,"node was active (inconsistency)"),t=C.extend({},e,{node:r.activeNode}),r.nodeSetActive(t,!1),k(null===r.activeNode,"deactivate was out of sync?")),o.activeVisible&&i.makeVisible({scrollIntoView:n}),r.activeNode=i,r.nodeRenderStatus(e),a||r.nodeSetFocus(e),s||r._triggerNodeEvent("activate",i,e.originalEvent)):(k(r.activeNode===i,"node was not active (inconsistency)"),r.activeNode=null,this.nodeRenderStatus(e),s||e.tree._triggerNodeEvent("deactivate",i,e.originalEvent)),T(i)))},nodeSetExpanded:function(i,r,e){var t,n,o,s,a,l,d=i.node,c=i.tree,u=i.options,h=!0===(e=e||{}).noAnimation,f=!0===e.noEvents;if(r=!1!==r,C(d.li).hasClass(u._classNames.animating))return d.warn("setExpanded("+r+") while animating: ignored."),A(d,["recursion"]);if(d.expanded&&r||!d.expanded&&!r)return T(d);if(r&&!d.lazy&&!d.hasChildren())return T(d);if(!r&&d.getLevel()<u.minExpandLevel)return A(d,["locked"]);if(!f&&!1===this._triggerNodeEvent("beforeExpand",d,i.originalEvent))return A(d,["rejected"]);if(h||d.isVisible()||(h=e.noAnimation=!0),n=new C.Deferred,r&&!d.expanded&&u.autoCollapse){a=d.getParentList(!1,!0),l=u.autoCollapse;try{for(u.autoCollapse=!1,o=0,s=a.length;o<s;o++)this._callHook("nodeCollapseSiblings",a[o],e)}finally{u.autoCollapse=l}}return n.done(function(){var e=d.getLastChild();r&&u.autoScroll&&!h&&e&&c._enableUpdate?e.scrollIntoView(!0,{topNode:d}).always(function(){f||i.tree._triggerNodeEvent(r?"expand":"collapse",i)}):f||i.tree._triggerNodeEvent(r?"expand":"collapse",i)}),t=function(e){var t=u._classNames,n=u.toggleEffect;if(d.expanded=r,c._callHook("treeStructureChanged",i,r?"expand":"collapse"),c._callHook("nodeRender",i,!1,!1,!0),d.ul)if("none"!==d.ul.style.display==!!d.expanded)d.warn("nodeSetExpanded: UL.style.display already set");else{if(n&&!h)return C(d.li).addClass(t.animating),void(N(C(d.ul)[n.effect])?C(d.ul)[n.effect]({duration:n.duration,always:function(){C(this).removeClass(t.animating),C(d.li).removeClass(t.animating),e()}}):(C(d.ul).stop(!0,!0),C(d.ul).parent().find(".ui-effects-placeholder").remove(),C(d.ul).toggle(n.effect,n.options,n.duration,function(){C(this).removeClass(t.animating),C(d.li).removeClass(t.animating),e()})));d.ul.style.display=d.expanded||!parent?"":"none"}e()},r&&d.lazy&&void 0===d.hasChildren()?d.load().done(function(){n.notifyWith&&n.notifyWith(d,["loaded"]),t(function(){n.resolveWith(d)})}).fail(function(e){t(function(){n.rejectWith(d,["load failed ("+e+")"])})}):t(function(){n.resolveWith(d)}),n.promise()},nodeSetFocus:function(e,t){var n,i=e.tree,r=e.node,o=i.options,s=!!e.originalEvent&&C(e.originalEvent.target).is(":input");if(t=!1!==t,i.focusNode){if(i.focusNode===r&&t)return;n=C.extend({},e,{node:i.focusNode}),i.focusNode=null,this._triggerNodeEvent("blur",n),this._callHook("nodeRenderStatus",n)}t&&(this.hasFocus()||(r.debug("nodeSetFocus: forcing container focus"),this._callHook("treeSetFocus",e,!0,{calledByNode:!0})),r.makeVisible({scrollIntoView:!1}),i.focusNode=r,o.titlesTabbable&&(s||C(r.span).find(".fancytree-title").focus()),o.aria&&C(i.$container).attr("aria-activedescendant",C(r.tr||r.li).uniqueId().attr("id")),this._triggerNodeEvent("focus",e),document.activeElement===i.$container.get(0)||1<=C(document.activeElement,i.$container).length||C(i.$container).focus(),o.autoScroll&&r.scrollIntoView(),this._callHook("nodeRenderStatus",e))},nodeSetSelected:function(e,t,n){var i=e.node,r=e.tree,o=e.options,s=!0===(n=n||{}).noEvents,a=i.parent;if(t=!1!==t,!h.evalOption("unselectable",i,i,o,!1))return i._lastSelectIntent=t,!!i.selected!==t||3===o.selectMode&&i.partsel&&!t?s||!1!==this._triggerNodeEvent("beforeSelect",i,e.originalEvent)?(t&&1===o.selectMode?(r.lastSelectedNode&&r.lastSelectedNode.setSelected(!1),i.selected=t):3!==o.selectMode||!a||a.radiogroup||i.radiogroup?a&&a.radiogroup?i.visitSiblings(function(e){e._changeSelectStatusAttrs(t&&e===i)},!0):i.selected=t:(i.selected=t,i.fixSelection3AfterClick(n)),this.nodeRenderStatus(e),r.lastSelectedNode=t?i:null,void(s||r._triggerNodeEvent("select",e))):!!i.selected:t},nodeSetStatus:function(i,e,t,n){var r=i.node,o=i.tree;function s(e,t){var n=r.children?r.children[0]:null;return n&&n.isStatusNode()?(C.extend(n,e),n.statusNodeType=t,o._callHook("nodeRenderTitle",n)):(r._setChildren([e]),o._callHook("treeStructureChanged",i,"setStatusNode"),r.children[0].statusNodeType=t,o.render()),r.children[0]}switch(e){case"ok":!function(){var e=r.children?r.children[0]:null;if(e&&e.isStatusNode()){try{r.ul&&(r.ul.removeChild(e.li),e.li=null)}catch(e){}1===r.children.length?r.children=[]:r.children.shift(),o._callHook("treeStructureChanged",i,"clearStatusNode")}}(),r._isLoading=!1,r._error=null,r.renderStatus();break;case"loading":r.parent||s({title:o.options.strings.loading+(t?" ("+t+")":""),checkbox:!1,tooltip:n},e),r._isLoading=!0,r._error=null,r.renderStatus();break;case"error":s({title:o.options.strings.loadError+(t?" ("+t+")":""),checkbox:!1,tooltip:n},e),r._isLoading=!1,r._error={message:t,details:n},r.renderStatus();break;case"nodata":s({title:t||o.options.strings.noData,checkbox:!1,tooltip:n},e),r._isLoading=!1,r._error=null,r.renderStatus();break;default:C.error("invalid node status "+e)}},nodeToggleExpanded:function(e){return this.nodeSetExpanded(e,!e.node.expanded)},nodeToggleSelected:function(e){var t=e.node,n=!t.selected;return t.partsel&&!t.selected&&!0===t._lastSelectIntent&&(t.selected=!(n=!1)),t._lastSelectIntent=n,this.nodeSetSelected(e,n)},treeClear:function(e){var t=e.tree;t.activeNode=null,t.focusNode=null,t.$div.find(">ul.fancytree-container").empty(),t.rootNode.children=null,t._callHook("treeStructureChanged",e,"clear")},treeCreate:function(e){},treeDestroy:function(e){this.$div.find(">ul.fancytree-container").remove(),this.$source&&this.$source.removeClass("fancytree-helper-hidden")},treeInit:function(e){var n=e.tree,i=n.options;n.$container.attr("tabindex",i.tabindex),C.each(m,function(e,t){void 0!==i[t]&&(n.info("Move option "+t+" to tree"),n[t]=i[t],delete i[t])}),i.checkboxAutoHide&&n.$container.addClass("fancytree-checkbox-auto-hide"),i.rtl?n.$container.attr("DIR","RTL").addClass("fancytree-rtl"):n.$container.removeAttr("DIR").removeClass("fancytree-rtl"),i.aria&&(n.$container.attr("role","tree"),1!==i.selectMode&&n.$container.attr("aria-multiselectable",!0)),this.treeLoad(e)},treeLoad:function(e,t){var n,i,r,o=e.tree,s=e.widget.element,a=C.extend({},e,{node:this.rootNode});if(o.rootNode.children&&this.treeClear(e),t=t||this.options.source)"string"==typeof t&&C.error("Not implemented");else switch(i=s.data("type")||"html"){case"html":(r=s.find(">ul").not(".fancytree-container").first()).length?(r.addClass("ui-fancytree-source fancytree-helper-hidden"),t=C.ui.fancytree.parseHtml(r),this.data=C.extend(this.data,P(r))):(h.warn("No `source` option was passed and container does not contain `<ul>`: assuming `source: []`."),t=[]);break;case"json":t=C.parseJSON(s.text()),s.contents().filter(function(){return 3===this.nodeType}).remove(),C.isPlainObject(t)&&(k(_(t.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),t=(n=t).children,delete n.children,C.each(m,function(e,t){void 0!==n[t]&&(o[t]=n[t],delete n[t])}),C.extend(o.data,n));break;default:C.error("Invalid data-type: "+i)}return o._triggerTreeEvent("preInit",null),this.nodeLoadChildren(a,t).done(function(){o._callHook("treeStructureChanged",e,"loadChildren"),o.render(),3===e.options.selectMode&&o.rootNode.fixSelection3FromEndNodes(),o.activeNode&&o.options.activeVisible&&o.activeNode.makeVisible(),o._triggerTreeEvent("init",null,{status:!0})}).fail(function(){o.render(),o._triggerTreeEvent("init",null,{status:!1})})},treeRegisterNode:function(e,t,n){e.tree._callHook("treeStructureChanged",e,t?"addNode":"removeNode")},treeSetFocus:function(e,t,n){var i;(t=!1!==t)!==this.hasFocus()&&(!(this._hasFocus=t)&&this.focusNode?this.focusNode.setFocus(!1):!t||n&&n.calledByNode||C(this.$container).focus(),this.$container.toggleClass("fancytree-treefocus",t),this._triggerTreeEvent(t?"focusTree":"blurTree"),t&&!this.activeNode&&(i=this._lastMousedownNode||this.getFirstChild())&&i.setFocus())},treeSetOption:function(e,t,n){var i=e.tree,r=!0,o=!1,s=!1;switch(t){case"aria":case"checkbox":case"icon":case"minExpandLevel":case"tabindex":s=o=!0;break;case"checkboxAutoHide":i.$container.toggleClass("fancytree-checkbox-auto-hide",!!n);break;case"escapeTitles":case"tooltip":s=!0;break;case"rtl":!1===n?i.$container.removeAttr("DIR").removeClass("fancytree-rtl"):i.$container.attr("DIR","RTL").addClass("fancytree-rtl"),s=!0;break;case"source":r=!1,i._callHook("treeLoad",i,n),s=!0}i.debug("set option "+t+"="+n+" <"+typeof n+">"),r&&(this.widget._super||C.Widget.prototype._setOption).call(this.widget,t,n),o&&i._callHook("treeCreate",i),s&&i.render(!0,!1)},treeStructureChanged:function(e,t){}}),C.widget("ui.fancytree",{options:{activeVisible:!0,ajax:{type:"GET",cache:!1,dataType:"json"},aria:!0,autoActivate:!0,autoCollapse:!1,autoScroll:!1,checkbox:!1,clickFolderMode:4,copyFunctionsToData:!1,debugLevel:null,disabled:!1,enableAspx:42,escapeTitles:!1,extensions:[],focusOnSelect:!1,generateIds:!1,icon:!0,idPrefix:"ft_",keyboard:!0,keyPathSeparator:"/",minExpandLevel:1,nodata:!0,quicksearch:!1,rtl:!1,scrollOfs:{top:0,bottom:0},scrollParent:null,selectMode:2,strings:{loading:"Loading...",loadError:"Load error!",moreData:"More...",noData:"No data."},tabindex:"0",titlesTabbable:!1,toggleEffect:{effect:"slideToggle",duration:200},tooltip:!1,treeId:null,_classNames:{active:"fancytree-active",animating:"fancytree-animating",combinedExpanderPrefix:"fancytree-exp-",combinedIconPrefix:"fancytree-ico-",error:"fancytree-error",expanded:"fancytree-expanded",focused:"fancytree-focused",folder:"fancytree-folder",hasChildren:"fancytree-has-children",lastsib:"fancytree-lastsib",lazy:"fancytree-lazy",loading:"fancytree-loading",node:"fancytree-node",partload:"fancytree-partload",partsel:"fancytree-partsel",radio:"fancytree-radio",selected:"fancytree-selected",statusNodePrefix:"fancytree-statusnode-",unselectable:"fancytree-unselectable"},lazyLoad:null,postProcess:null},_deprecationWarning:function(e){var t=this.tree;t&&3<=t.options.debugLevel&&t.warn("$().fancytree('"+e+"') is deprecated (see https://wwwendt.de/tech/fancytree/doc/jsdoc/Fancytree_Widget.html")},_create:function(){this.tree=new F(this),this.$source=this.source||"json"===this.element.data("type")?this.element:this.element.find(">ul").first();for(var e,t,n=this.options,i=n.extensions,r=(this.tree,0);r<i.length;r++)t=i[r],(e=C.ui.fancytree._extensions[t])||C.error("Could not apply extension '"+t+"' (it is not registered, did you forget to include it?)"),this.tree.options[t]=function e(t){var n,i,r,o,s=t||{},a=1,l=arguments.length;if("object"==typeof s||N(s)||(s={}),a===l)throw Error("need at least two args");for(;a<l;a++)if(null!=(n=arguments[a]))for(i in n)w(n,i)&&(o=s[i],s!==(r=n[i])&&(r&&C.isPlainObject(r)?(o=o&&C.isPlainObject(o)?o:{},s[i]=e(o,r)):void 0!==r&&(s[i]=r)));return s}({},e.options,this.tree.options[t]),k(void 0===this.tree.ext[t],"Extension name must not exist as Fancytree.ext attribute: '"+t+"'"),this.tree.ext[t]={},function(e,t,n){for(var i in t)"function"==typeof t[i]?"function"==typeof e[i]?e[i]=E(i,e,0,t,n):"_"===i.charAt(0)?e.ext[n][i]=E(i,e,0,t,n):C.error("Could not override tree."+i+". Use prefix '_' to create tree."+n+"._"+i):"options"!==i&&(e.ext[n][i]=t[i])}(this.tree,e,t),0;void 0!==n.icons&&(!0===n.icon?(this.tree.warn("'icons' tree option is deprecated since v2.14.0: use 'icon' instead"),n.icon=n.icons):C.error("'icons' tree option is deprecated since v2.14.0: use 'icon' only instead")),void 0!==n.iconClass&&(n.icon?C.error("'iconClass' tree option is deprecated since v2.14.0: use 'icon' only instead"):(this.tree.warn("'iconClass' tree option is deprecated since v2.14.0: use 'icon' instead"),n.icon=n.iconClass)),void 0!==n.tabbable&&(n.tabindex=n.tabbable?"0":"-1",this.tree.warn("'tabbable' tree option is deprecated since v2.17.0: use 'tabindex='"+n.tabindex+"' instead")),this.tree._callHook("treeCreate",this.tree)},_init:function(){this.tree._callHook("treeInit",this.tree),this._bind()},_setOption:function(e,t){return this.tree._callHook("treeSetOption",this.tree,e,t)},_destroy:function(){this._unbind(),this.tree._callHook("treeDestroy",this.tree)},_unbind:function(){var e=this.tree._ns;this.element.off(e),this.tree.$container.off(e),C(document).off(e)},_bind:function(){var s=this,a=this.options,o=this.tree,e=o._ns;this._unbind(),o.$container.on("focusin"+e+" focusout"+e,function(e){var t=h.getNode(e),n="focusin"===e.type;if(!n&&t&&C(e.target).is("a"))t.debug("Ignored focusout on embedded <a> element.");else{if(n){if(o._getExpiringValue("focusin"))return void o.debug("Ignored double focusin.");o._setExpiringValue("focusin",!0,50),t||(t=o._getExpiringValue("mouseDownNode"))&&o.debug("Reconstruct mouse target for focusin from recent event.")}t?o._callHook("nodeSetFocus",o._makeHookContext(t,e),n):o.tbody&&C(e.target).parents("table.fancytree-container > thead").length?o.debug("Ignore focus event outside table body.",e):o._callHook("treeSetFocus",o,n)}}).on("selectstart"+e,"span.fancytree-title",function(e){e.preventDefault()}).on("keydown"+e,function(e){if(a.disabled||!1===a.keyboard)return!0;var t,n=o.focusNode,i=o._makeHookContext(n||o,e),r=o.phase;try{return o.phase="userEvent","preventNav"===(t=n?o._triggerNodeEvent("keydown",n,e):o._triggerTreeEvent("keydown",e))?t=!0:!1!==t&&(t=o._callHook("nodeKeydown",i)),t}finally{o.phase=r}}).on("mousedown"+e,function(e){e=h.getEventTarget(e);o._lastMousedownNode=e?e.node:null,o._setExpiringValue("mouseDownNode",o._lastMousedownNode)}).on("click"+e+" dblclick"+e,function(e){if(a.disabled)return!0;var t,n=h.getEventTarget(e),i=n.node,r=s.tree,o=r.phase;if(!i)return!0;t=r._makeHookContext(i,e);try{switch(r.phase="userEvent",e.type){case"click":return t.targetType=n.type,i.isPagingNode()?!0===r._triggerNodeEvent("clickPaging",t,e):!1!==r._triggerNodeEvent("click",t,e)&&r._callHook("nodeClick",t);case"dblclick":return t.targetType=n.type,!1!==r._triggerNodeEvent("dblclick",t,e)&&r._callHook("nodeDblclick",t)}}finally{r.phase=o}})},getActiveNode:function(){return this._deprecationWarning("getActiveNode"),this.tree.activeNode},getNodeByKey:function(e){return this._deprecationWarning("getNodeByKey"),this.tree.getNodeByKey(e)},getRootNode:function(){return this._deprecationWarning("getRootNode"),this.tree.rootNode},getTree:function(){return this._deprecationWarning("getTree"),this.tree}}),h=C.ui.fancytree,C.extend(C.ui.fancytree,{version:"2.38.2",buildType: "production",debugLevel: 3,_nextId:1,_nextNodeKey:1,_extensions:{},_FancytreeClass:F,_FancytreeNodeClass:j,jquerySupports:{positionMyOfs:function(e){for(var t,n,i=C.map(S(e).split("."),function(e){return parseInt(e,10)}),r=C.map(Array.prototype.slice.call(arguments,1),function(e){return parseInt(e,10)}),o=0;o<r.length;o++)if((t=i[o]||0)!==(n=r[o]||0))return n<t;return!0}(C.ui.version,1,9)},assert:k,createTree:function(e,t){t=C(e).fancytree(t);return h.getTree(t)},debounce:function(t,n,i,r){var o;return 3===arguments.length&&"boolean"!=typeof i&&(r=i,i=!1),function(){var e=arguments;r=r||this,i&&!o&&n.apply(r,e),clearTimeout(o),o=setTimeout(function(){i||n.apply(r,e),o=null},t)}},debug:function(e){4<=C.ui.fancytree.debugLevel&&d("log",arguments)},error:function(e){1<=C.ui.fancytree.debugLevel&&d("error",arguments)},escapeHtml:function(e){return(""+e).replace(t,function(e){return i[e]})},fixPositionOptions:function(e){var t,n,i,r;return(e.offset||0<=(""+e.my+e.at).indexOf("%"))&&C.error("expected new position syntax (but '%' is not supported)"),C.ui.fancytree.jquerySupports.positionMyOfs||(t=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(e.my),n=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(e.at),i=(t[2]?+t[2]:0)+(n[2]?+n[2]:0),r=(t[4]?+t[4]:0)+(n[4]?+n[4]:0),e=C.extend({},e,{my:t[1]+" "+t[3],at:n[1]+" "+n[3]}),(i||r)&&(e.offset=i+" "+r)),e},getEventTarget:function(e){var t=e&&e.target?e.target.className:"",n={node:this.getNode(e.target),type:void 0};return/\bfancytree-title\b/.test(t)?n.type="title":/\bfancytree-expander\b/.test(t)?n.type=!1===n.node.hasChildren()?"prefix":"expander":/\bfancytree-checkbox\b/.test(t)?n.type="checkbox":/\bfancytree(-custom)?-icon\b/.test(t)?n.type="icon":/\bfancytree-node\b/.test(t)?n.type="title":e&&e.target&&((e=C(e.target)).is("ul[role=group]")?((n.node&&n.node.tree||h).debug("Ignoring click on outer UL."),n.node=null):e.closest(".fancytree-title").length?n.type="title":e.closest(".fancytree-checkbox").length?n.type="checkbox":e.closest(".fancytree-expander").length&&(n.type="expander")),n},getEventTargetType:function(e){return this.getEventTarget(e).type},getNode:function(e){if(e instanceof j)return e;for(e instanceof C?e=e[0]:void 0!==e.originalEvent&&(e=e.target);e;){if(e.ftnode)return e.ftnode;e=e.parentNode}return null},getTree:function(e){var t=e;return e instanceof F?e:("number"==typeof(e=void 0===e?0:e)?e=C(".fancytree-container").eq(e):"string"==typeof e?(e=C("#ft-id-"+t).eq(0)).length||(e=C(t).eq(0)):e instanceof Element||e instanceof HTMLDocument?e=C(e):e instanceof C?e=e.eq(0):void 0!==e.originalEvent&&(e=C(e.target)),(e=(e=e.closest(":ui-fancytree")).data("ui-fancytree")||e.data("fancytree"))?e.tree:null)},evalOption:function(e,t,n,i,r){var o,s=t.tree,i=i[e],n=n[e];return N(i)?(o={node:t,tree:s,widget:s.widget,options:s.widget.options,typeInfo:s.types[t.type]||{}},null==(o=i.call(s,{type:e},o))&&(o=n)):o=null==n?i:n,o=null==o?r:o},setSpanIcon:function(e,t,n){var i=C(e);"string"==typeof n?i.attr("class",t+" "+n):(n.text?i.text(""+n.text):n.html&&(e.innerHTML=n.html),i.attr("class",t+" "+(n.addClass||"")))},eventToString:function(e){var t=e.which,n=e.type,i=[];return e.altKey&&i.push("alt"),e.ctrlKey&&i.push("ctrl"),e.metaKey&&i.push("meta"),e.shiftKey&&i.push("shift"),"click"===n||"dblclick"===n?i.push(o[e.button]+n):"wheel"===n?i.push(n):r[t]||i.push(u[t]||String.fromCharCode(t).toLowerCase()),i.join("+")},info:function(e){3<=C.ui.fancytree.debugLevel&&d("info",arguments)},keyEventToString:function(e){return this.warn("keyEventToString() is deprecated: use eventToString()"),this.eventToString(e)},overrideMethod:function(e,t,n,i){var r,o=e[t]||C.noop;e[t]=function(){var e=i||this;try{return r=e._super,e._super=o,n.apply(e,arguments)}finally{e._super=r}}},parseHtml:function(s){var a,l,d,c,u,h,f,p,e=s.find(">li"),g=[];return e.each(function(){var e,t,n=C(this),i=n.find(">span",this).first(),r=i.length?null:n.find(">a").first(),o={tooltip:null,data:{}};for(i.length?o.title=i.html():r&&r.length?(o.title=r.html(),o.data.href=r.attr("href"),o.data.target=r.attr("target"),o.tooltip=r.attr("title")):(o.title=n.html(),0<=(u=o.title.search(/<ul/i))&&(o.title=o.title.substring(0,u))),o.title=S(o.title),c=0,h=y.length;c<h;c++)o[y[c]]=void 0;for(a=this.className.split(" "),d=[],c=0,h=a.length;c<h;c++)l=a[c],v[l]?o[l]=!0:d.push(l);if(o.extraClasses=d.join(" "),(f=n.attr("title"))&&(o.tooltip=f),(f=n.attr("id"))&&(o.key=f),n.attr("hideCheckbox")&&(o.checkbox=!1),(e=P(n))&&!C.isEmptyObject(e)){for(t in x)w(e,t)&&(e[x[t]]=e[t],delete e[t]);for(c=0,h=b.length;c<h;c++)f=b[c],null!=(p=e[f])&&(delete e[f],o[f]=p);C.extend(o.data,e)}(s=n.find(">ul").first()).length?o.children=C.ui.fancytree.parseHtml(s):o.children=o.lazy?void 0:null,g.push(o)}),g},registerExtension:function(e){k(null!=e.name,"extensions must have a `name` property."),k(null!=e.version,"extensions must have a `version` property."),C.ui.fancytree._extensions[e.name]=e},trim:S,unescapeHtml:function(e){var t=document.createElement("div");return t.innerHTML=e,0===t.childNodes.length?"":t.childNodes[0].nodeValue},warn:function(e){2<=C.ui.fancytree.debugLevel&&d("warn",arguments)}}),C.ui.fancytree}function k(e,t){e||(C.ui.fancytree.error(t="Fancytree assertion failed"+(t=t?": "+t:"")),C.error(t))}function w(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function N(e){return"function"==typeof e}function S(e){return null==e?"":e.trim()}function d(t,n){var i,r,t=window.console?window.console[t]:null;if(t)try{t.apply(window.console,n)}catch(e){for(r="",i=0;i<n.length;i++)r+=n[i];t(r)}}function E(e,i,t,n,r){var o,s,a;function l(){return o.apply(i,arguments)}function d(e){return o.apply(i,e)}return o=i[e],s=n[e],a=i.ext[r],function(){var e=i._local,t=i._super,n=i._superApply;try{return i._local=a,i._super=l,i._superApply=d,s.apply(i,arguments)}finally{i._local=e,i._super=t,i._superApply=n}}}function T(e,t){return(void 0===e?C.Deferred(function(){this.resolve()}):C.Deferred(function(){this.resolveWith(e,t)})).promise()}function A(e,t){return(void 0===e?C.Deferred(function(){this.reject()}):C.Deferred(function(){this.rejectWith(e,t)})).promise()}function O(e,t){return function(){e.resolveWith(t)}}function P(e){var t=C.extend({},e.data()),e=t.json;return delete t.fancytree,delete t.uiFancytree,e&&(delete t.json,t=C.extend(t,e)),t}function D(e){return(""+e).replace(n,function(e){return i[e]})}function L(t){return t=t.toLowerCase(),function(e){return 0<=e.title.toLowerCase().indexOf(t)}}function j(e,t){var n,i,r;for(this.parent=e,this.tree=e.tree,this.ul=null,this.li=null,this.statusNodeType=null,this._isLoading=!1,this._error=null,this.data={},n=0,i=b.length;n<i;n++)this[r=b[n]]=t[r];for(r in null==this.unselectableIgnore&&null==this.unselectableStatus||(this.unselectable=!0),t.hideCheckbox&&C.error("'hideCheckbox' node option was removed in v2.23.0: use 'checkbox: false'"),t.data&&C.extend(this.data,t.data),t)s[r]||!this.tree.options.copyFunctionsToData&&N(t[r])||a[r]||(this.data[r]=t[r]);null==this.key?this.tree.options.defaultKey?(this.key=""+this.tree.options.defaultKey(this),k(this.key,"defaultKey() must return a unique key")):this.key="_"+h._nextNodeKey++:this.key=""+this.key,t.active&&(k(null===this.tree.activeNode,"only one active node allowed"),this.tree.activeNode=this),t.selected&&(this.tree.lastSelectedNode=this),(e=t.children)?e.length?this._setChildren(e):this.children=this.lazy?[]:null:this.children=null,this.tree._callHook("treeRegisterNode",this.tree,!0,this)}function F(e){this.widget=e,this.$div=e.element,this.options=e.options,this.options&&(void 0!==this.options.lazyload&&C.error("The 'lazyload' event is deprecated since 2014-02-25. Use 'lazyLoad' (with uppercase L) instead."),void 0!==this.options.loaderror&&C.error("The 'loaderror' event was renamed since 2014-07-03. Use 'loadError' (with uppercase E) instead."),void 0!==this.options.fx&&C.error("The 'fx' option was replaced by 'toggleEffect' since 2014-11-30."),void 0!==this.options.removeNode&&C.error("The 'removeNode' event was replaced by 'modifyChild' since 2.20 (2016-09-10).")),this.ext={},this.types={},this.columns={},this.data=P(this.$div),this._id=""+(this.options.treeId||C.ui.fancytree._nextId++),this._ns=".fancytree-"+this._id,this.activeNode=null,this.focusNode=null,this._hasFocus=null,this._tempCache={},this._lastMousedownNode=null,this._enableUpdate=!0,this.lastSelectedNode=null,this.systemFocusElement=null,this.lastQuicksearchTerm="",this.lastQuicksearchTime=0,this.viewport=null,this.statusClassPropName="span",this.ariaPropName="li",this.nodeContainerAttrName="li",this.$div.find(">ul.fancytree-container").remove(),this.rootNode=new j({tree:this},{title:"root",key:"root_"+this._id,children:null,expanded:!0}),this.rootNode.parent=null,e=C("<ul>",{id:"ft-id-"+this._id,class:"ui-fancytree fancytree-container fancytree-plain"}).appendTo(this.$div),this.$container=e,this.rootNode.ul=e[0],null==this.options.debugLevel&&(this.options.debugLevel=h.debugLevel)}C.ui.fancytree.warn("Fancytree: ignored duplicate include")},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree.ui-deps"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree.ui-deps"),module.exports=t(require("jquery"))):t(jQuery),t=function(o){"use strict";return o.ui.fancytree._FancytreeClass.prototype.countSelected=function(e){this.options;return this.getSelectedNodes(e).length},o.ui.fancytree._FancytreeNodeClass.prototype.updateCounters=function(){var e=this,t=o("span.fancytree-childcounter",e.span),n=e.tree.options.childcounter,i=e.countChildren(n.deep);!(e.data.childCounter=i)&&n.hideZeros||e.isExpanded()&&n.hideExpanded?t.remove():(t=!t.length?o("<span class='fancytree-childcounter'/>").appendTo(o("span.fancytree-icon,span.fancytree-custom-icon",e.span)):t).text(i),!n.deep||e.isTopLevel()||e.isRootNode()||e.parent.updateCounters()},o.ui.fancytree.prototype.widgetMethod1=function(e){this.tree;return e},o.ui.fancytree.registerExtension({name:"childcounter",version:"2.38.2",options:{deep:!0,hideZeros:!0,hideExpanded:!1},foo:42,_appendCounter:function(e){},treeInit:function(e){e.options,e.options.childcounter;this._superApply(arguments),this.$container.addClass("fancytree-ext-childcounter")},treeDestroy:function(e){this._superApply(arguments)},nodeRenderTitle:function(e,t){var n=e.node,i=e.options.childcounter,r=null==n.data.childCounter?n.countChildren(i.deep):+n.data.childCounter;this._super(e,t),!r&&i.hideZeros||n.isExpanded()&&i.hideExpanded||o("span.fancytree-icon,span.fancytree-custom-icon",n.span).append(o("<span class='fancytree-childcounter'/>").text(r))},nodeSetExpanded:function(e,t,n){var i=e.tree;e.node;return this._superApply(arguments).always(function(){i.nodeRenderTitle(e)})}}),o.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),t=function(c){"use strict";var u=c.ui.fancytree.assert;function n(e,t,n){for(var i,r,o=3&e.length,s=e.length-o,a=n,l=3432918353,d=461845907,c=0;c<s;)r=255&e.charCodeAt(c)|(255&e.charCodeAt(++c))<<8|(255&e.charCodeAt(++c))<<16|(255&e.charCodeAt(++c))<<24,++c,a=27492+(65535&(i=5*(65535&(a=(a^=r=(65535&(r=(r=(65535&r)*l+(((r>>>16)*l&65535)<<16)&4294967295)<<15|r>>>17))*d+(((r>>>16)*d&65535)<<16)&4294967295)<<13|a>>>19))+((5*(a>>>16)&65535)<<16)&4294967295))+((58964+(i>>>16)&65535)<<16);switch(r=0,o){case 3:r^=(255&e.charCodeAt(c+2))<<16;case 2:r^=(255&e.charCodeAt(c+1))<<8;case 1:a^=r=(65535&(r=(r=(65535&(r^=255&e.charCodeAt(c)))*l+(((r>>>16)*l&65535)<<16)&4294967295)<<15|r>>>17))*d+(((r>>>16)*d&65535)<<16)&4294967295}return a^=e.length,a=2246822507*(65535&(a^=a>>>16))+((2246822507*(a>>>16)&65535)<<16)&4294967295,a=3266489909*(65535&(a^=a>>>13))+((3266489909*(a>>>16)&65535)<<16)&4294967295,a^=a>>>16,t?("0000000"+(a>>>0).toString(16)).substr(-8):a>>>0}return c.ui.fancytree._FancytreeNodeClass.prototype.getCloneList=function(e){var t,n=this.tree,i=n.refMap[this.refKey]||null,r=n.keyMap;return i&&(t=this.key,e?i=c.map(i,function(e){return r[e]}):(i=c.map(i,function(e){return e===t?null:r[e]})).length<1&&(i=null)),i},c.ui.fancytree._FancytreeNodeClass.prototype.isClone=function(){var e=this.refKey||null,e=e&&this.tree.refMap[e]||null;return!!(e&&1<e.length)},c.ui.fancytree._FancytreeNodeClass.prototype.reRegister=function(t,e){e=null==e?null:""+e;var n=this.tree,i=this.key,r=this.refKey,o=n.keyMap,s=n.refMap,a=s[r]||null,n=!1;return null!=(t=null==t?null:""+t)&&t!==this.key&&(o[t]&&c.error("[ext-clones] reRegister("+t+"): already exists: "+this),delete o[i],o[t]=this,a&&(s[r]=c.map(a,function(e){return e===i?t:e})),this.key=t,n=!0),null!=e&&e!==this.refKey&&(a&&(1===a.length?delete s[r]:s[r]=c.map(a,function(e){return e===i?null:e})),s[e]?s[e].append(t):s[e]=[this.key],this.refKey=e,n=!0),n},c.ui.fancytree._FancytreeNodeClass.prototype.setRefKey=function(e){return this.reRegister(null,e)},c.ui.fancytree._FancytreeClass.prototype.getNodesByRef=function(e,t){var n=this.keyMap,e=this.refMap[e]||null;return e=e&&(e=t?c.map(e,function(e){e=n[e];return e.isDescendantOf(t)?e:null}):c.map(e,function(e){return n[e]})).length<1?null:e},c.ui.fancytree._FancytreeClass.prototype.changeRefKey=function(e,t){var n,i=this.keyMap,r=this.refMap[e]||null;if(r){for(n=0;n<r.length;n++)i[r[n]].refKey=t;delete this.refMap[e],this.refMap[t]=r}},c.ui.fancytree.registerExtension({name:"clones",version:"2.38.2",options:{highlightActiveClones:!0,highlightClones:!1},treeCreate:function(e){this._superApply(arguments),e.tree.refMap={},e.tree.keyMap={}},treeInit:function(e){this.$container.addClass("fancytree-ext-clones"),u(null==e.options.defaultKey),e.options.defaultKey=function(e){return t=e,"id_"+(t=n(e=(e=c.map(e.getParentList(!1,!0),function(e){return e.refKey||e.key})).join("/"),!0))+n(t+e,!0);var t},this._superApply(arguments)},treeClear:function(e){return e.tree.refMap={},e.tree.keyMap={},this._superApply(arguments)},treeRegisterNode:function(e,t,n){var i,r,o=e.tree,s=o.keyMap,a=o.refMap,l=n.key,d=n&&null!=n.refKey?""+n.refKey:null;return n.isStatusNode()||(t?(null!=s[n.key]&&(r=s[n.key],r="clones.treeRegisterNode: duplicate key '"+n.key+"': /"+n.getPath(!0)+" => "+r.getPath(!0),o.error(r),c.error(r)),s[l]=n,d&&((i=a[d])?(i.push(l),2===i.length&&e.options.clones.highlightClones&&s[i[0]].renderStatus()):a[d]=[l])):(null==s[l]&&c.error("clones.treeRegisterNode: node.key not registered: "+n.key),delete s[l],d&&(i=a[d])&&((r=i.length)<=1?(u(1===r),u(i[0]===l),delete a[d]):(function(e,t){for(var n=e.length-1;0<=n;n--)if(e[n]===t)return e.splice(n,1)}(i,l),2===r&&e.options.clones.highlightClones&&s[i[0]].renderStatus())))),this._super(e,t,n)},nodeRenderStatus:function(e){var t,n=e.node,i=this._super(e);return e.options.clones.highlightClones&&(t=c(n[e.tree.statusClassPropName])).length&&n.isClone()&&t.addClass("fancytree-clone"),i},nodeSetActive:function(e,n,t){var i=e.tree.statusClassPropName,r=e.node,o=this._superApply(arguments);return e.options.clones.highlightActiveClones&&r.isClone()&&c.each(r.getCloneList(!0),function(e,t){c(t[i]).toggleClass("fancytree-active-clone",!1!==n)}),o}}),c.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),t=function(f){"use strict";var a,l,p=f.ui.fancytree,o=/Mac/.test(navigator.platform),d="fancytree-drag-source",c="fancytree-drag-remove",g="fancytree-drop-accept",y="fancytree-drop-after",v="fancytree-drop-before",m="fancytree-drop-over",b="fancytree-drop-reject",x="fancytree-drop-target",u="application/x-fancytree-node",C=null,h=null,_=null,k=null,w=null,s=null,N=null,S=null,E=null,T=null;function A(){_=h=s=S=N=T=w=null,k&&k.removeClass(d+" "+c),k=null,C&&C.hide(),l&&(l.remove(),l=null)}function O(e){return 0===e?"":0<e?"+"+e:""+e}function P(e,t){var n,i=t.tree,r=t.dataTransfer;"dragstart"===e.type?(t.effectAllowed=i.options.dnd5.effectAllowed,t.dropEffect=i.options.dnd5.dropEffectDefault):(t.effectAllowed=S,t.dropEffect=N),t.dropEffectSuggested=(n=e,i=(e=i).options.dnd5.dropEffectDefault,o?n.metaKey&&n.altKey||n.ctrlKey?i="link":n.metaKey?i="move":n.altKey&&(i="copy"):n.ctrlKey?i="copy":n.shiftKey?i="move":n.altKey&&(i="link"),i!==s&&e.info("evalEffectModifiers: "+n.type+" - evalEffectModifiers(): "+s+" -> "+i),s=i),t.isMove="move"===t.dropEffect,t.files=r.files||[]}function D(e,t,n){var i=t.tree,r=t.dataTransfer;return"dragstart"!==e.type&&S!==t.effectAllowed&&i.warn("effectAllowed should only be changed in dragstart event: "+e.type+": data.effectAllowed changed from "+S+" -> "+t.effectAllowed),!1===n&&(i.info("applyDropEffectCallback: allowDrop === false"),t.effectAllowed="none",t.dropEffect="none"),t.isMove="move"===t.dropEffect,"dragstart"===e.type&&(S=t.effectAllowed,N=t.dropEffect),r.effectAllowed=S,r.dropEffect=N}function L(e,t){if(t.options.dnd5.scroll&&(h=t.tree,s=e,r=h.options.dnd5,o=h.$scrollParent[0],l=r.scrollSensitivity,u=r.scrollSpeed,i=0,o!==document&&"HTML"!==o.tagName?(r=h.$scrollParent.offset(),d=o.scrollTop,r.top+o.offsetHeight-s.pageY<l?0<o.scrollHeight-h.$scrollParent.innerHeight()-d&&(o.scrollTop=i=d+u):0<d&&s.pageY-r.top<l&&(o.scrollTop=i=d-u)):0<(d=f(document).scrollTop())&&s.pageY-d<l?(i=d-u,f(document).scrollTop(i)):f(window).height()-(s.pageY-d)<l&&(i=d+u,f(document).scrollTop(i)),i&&h.debug("autoScroll: "+i+"px")),!t.node)return t.tree.warn("Ignored dragover for non-node"),E;var n,i,r=null,o=t.tree,s=o.options,a=s.dnd5,l=t.node,d=t.otherNode,c="center",u=f(l.span),h=u.find("span.fancytree-title");if(!1===w)return o.debug("Ignored dragover, since dragenter returned false."),!1;if("string"==typeof w&&f.error("assert failed: dragenter returned string"),i=u.offset(),u=(e.pageY-i.top)/u.height(),void 0===e.pageY&&o.warn("event.pageY is undefined: see issue #1013."),w.after&&.75<u||!w.over&&w.after&&.5<u?r="after":w.before&&u<=.25||!w.over&&w.before&&u<=.5?r="before":w.over&&(r="over"),a.preventVoidMoves&&"move"===t.dropEffect&&(l===d?(l.debug("Drop over source node prevented."),r=null):"before"===r&&d&&l===d.getNextSibling()?(l.debug("Drop after source node prevented."),r=null):"after"===r&&d&&l===d.getPrevSibling()?(l.debug("Drop before source node prevented."),r=null):"over"===r&&d&&d.parent===l&&d.isLastSibling()&&(l.debug("Drop last child over own parent prevented."),r=null)),(t.hitMode=r)&&a.dragOver&&(P(e,t),a.dragOver(l,t),D(e,t,!!r),r=t.hitMode),"after"===(E=r)||"before"===r||"over"===r){switch(n=a.dropMarkerOffsetX||0,r){case"before":c="top",n+=a.dropMarkerInsertOffsetX||0;break;case"after":c="bottom",n+=a.dropMarkerInsertOffsetX||0}h={my:"left"+O(n)+" center",at:"left "+c,of:h},s.rtl&&(h.my="right"+O(-n)+" center",h.at="right "+c),C.toggleClass(y,"after"===r).toggleClass(m,"over"===r).toggleClass(v,"before"===r).show().position(p.fixPositionOptions(h))}else C.hide();return f(l.span).toggleClass(x,"after"===r||"before"===r||"over"===r).toggleClass(y,"after"===r).toggleClass(v,"before"===r).toggleClass(g,"over"===r).toggleClass(b,!1===r),r}function j(e){var t,n=this,i=n.options.dnd5,r=null,o=p.getNode(e),s=e.dataTransfer||e.originalEvent.dataTransfer,a={tree:n,node:o,options:n.options,originalEvent:e.originalEvent,widget:n.widget,hitMode:w,dataTransfer:s,otherNode:h||null,otherNodeList:_||null,otherNodeData:null,useDefaultImage:!0,dropEffect:void 0,dropEffectSuggested:void 0,effectAllowed:void 0,files:null,isCancelled:void 0,isMove:void 0};switch(e.type){case"dragenter":if(T=null,!o){n.debug("Ignore non-node "+e.type+": "+e.target.tagName+"."+e.target.className),w=!1;break}if(f(o.span).addClass(m).removeClass(g+" "+b),t=0<=f.inArray(u,s.types),i.preventNonNodes&&!t){o.debug("Reject dropping a non-node."),w=!1;break}if(i.preventForeignNodes&&(!h||h.tree!==o.tree)){o.debug("Reject dropping a foreign node."),w=!1;break}if(i.preventSameParent&&a.otherNode&&a.otherNode.tree===o.tree&&o.parent===a.otherNode.parent){o.debug("Reject dropping as sibling (same parent)."),w=!1;break}if(i.preventRecursion&&a.otherNode&&a.otherNode.tree===o.tree&&o.isDescendantOf(a.otherNode)){o.debug("Reject dropping below own ancestor."),w=!1;break}if(i.preventLazyParents&&!o.isLoaded()){o.warn("Drop over unloaded target node prevented."),w=!1;break}C.show(),P(e,a),t=i.dragEnter(o,a),t=!!(t=t)&&(t=f.isPlainObject(t)?{over:!!t.over,before:!!t.before,after:!!t.after}:Array.isArray(t)?{over:0<=f.inArray("over",t),before:0<=f.inArray("before",t),after:0<=f.inArray("after",t)}:{over:!0===t||"over"===t,before:!0===t||"before"===t,after:!0===t||"after"===t},0!==Object.keys(t).length&&t),D(e,a,r=(w=t)&&(t.over||t.before||t.after));break;case"dragover":if(!o){n.debug("Ignore non-node "+e.type+": "+e.target.tagName+"."+e.target.className);break}P(e,a),r=!!(E=L(e,a)),("over"===E||!1===E)&&!o.expanded&&!1!==o.hasChildren()?T?!(i.autoExpandMS&&Date.now()-T>i.autoExpandMS)||o.isLoading()||i.dragExpand&&!1===i.dragExpand(o,a)||o.setExpanded():T=Date.now():T=null;break;case"dragleave":if(!o){n.debug("Ignore non-node "+e.type+": "+e.target.tagName+"."+e.target.className);break}if(!f(o.span).hasClass(m)){o.debug("Ignore dragleave (multi).");break}f(o.span).removeClass(m+" "+g+" "+b),o.scheduleAction("cancel"),i.dragLeave(o,a),C.hide();break;case"drop":if(0<=f.inArray(u,s.types)&&(d=s.getData(u),n.info(e.type+": getData('application/x-fancytree-node'): '"+d+"'")),d||(d=s.getData("text"),n.info(e.type+": getData('text'): '"+d+"'")),d)try{void 0!==(l=JSON.parse(d)).title&&(a.otherNodeData=l)}catch(e){}n.debug(e.type+": nodeData: '"+d+"', otherNodeData: ",a.otherNodeData),f(o.span).removeClass(m+" "+g+" "+b),a.hitMode=E,P(e,a),a.isCancelled=!E;var l=h&&h.span,d=h&&h.tree;i.dragDrop(o,a),e.preventDefault(),l&&!document.body.contains(l)&&(d===n?(n.debug("Drop handler removed source element: generating dragEnd."),i.dragEnd(h,a)):n.warn("Drop handler removed source element: dragend event may be lost.")),A()}if(r)return e.preventDefault(),!1}return f.ui.fancytree.getDragNodeList=function(){return _||[]},f.ui.fancytree.getDragNode=function(){return h},f.ui.fancytree.registerExtension({name:"dnd5",version:"2.38.2",options:{autoExpandMS:1500,dropMarkerInsertOffsetX:-16,dropMarkerOffsetX:-24,dropMarkerParent:"body",multiSource:!1,effectAllowed:"all",dropEffectDefault:"move",preventForeignNodes:!1,preventLazyParents:!0,preventNonNodes:!1,preventRecursion:!0,preventSameParent:!1,preventVoidMoves:!0,scroll:!0,scrollSensitivity:20,scrollSpeed:5,setTextTypeJson:!1,sourceCopyHook:null,dragStart:null,dragDrag:f.noop,dragEnd:f.noop,dragEnter:null,dragOver:f.noop,dragExpand:f.noop,dragDrop:f.noop,dragLeave:f.noop},treeInit:function(e){var t=e.tree,n=e.options,i=n.glyph||null,r=n.dnd5;0<=f.inArray("dnd",n.extensions)&&f.error("Extensions 'dnd' and 'dnd5' are mutually exclusive."),r.dragStop&&f.error("dragStop is not used by ext-dnd5. Use dragEnd instead."),null!=r.preventRecursiveMoves&&f.error("preventRecursiveMoves was renamed to preventRecursion."),r.dragStart&&p.overrideMethod(e.options,"createNode",function(e,t){this._super.apply(this,arguments),t.node.span?t.node.span.draggable=!0:t.node.warn("Cannot add `draggable`: no span tag")}),this._superApply(arguments),this.$container.addClass("fancytree-ext-dnd5"),e=f("<span>").appendTo(this.$container),this.$scrollParent=e.scrollParent(),e.remove(),(C=f("#fancytree-drop-marker")).length||(C=f("<div id='fancytree-drop-marker'></div>").hide().css({"z-index":1e3,"pointer-events":"none"}).prependTo(r.dropMarkerParent),i&&p.setSpanIcon(C[0],i.map._addClass,i.map.dropMarker)),C.toggleClass("fancytree-rtl",!!n.rtl),r.dragStart&&t.$container.on("dragstart drag dragend",function(e){var t=this,n=t.options.dnd5,i=p.getNode(e),r=e.dataTransfer||e.originalEvent.dataTransfer,o={tree:t,node:i,options:t.options,originalEvent:e.originalEvent,widget:t.widget,dataTransfer:r,useDefaultImage:!0,dropEffect:void 0,dropEffectSuggested:void 0,effectAllowed:void 0,files:void 0,isCancelled:void 0,isMove:void 0};switch(e.type){case"dragstart":if(!i)return t.info("Ignored dragstart on a non-node."),!1;h=i,_=!1===n.multiSource?[i]:!0===n.multiSource?i.isSelected()?t.getSelectedNodes():[i]:n.multiSource(i,o),(k=f(f.map(_,function(e){return e.span}))).addClass(d);var s=i.toDict(!0,n.sourceCopyHook);s.treeId=i.tree._id,s=JSON.stringify(s);try{r.setData(u,s),r.setData("text/html",f(i.span).html()),r.setData("text/plain",i.title)}catch(e){t.warn("Could not set data (IE only accepts 'text') - "+e)}return(n.setTextTypeJson?r.setData("text",s):r.setData("text",i.title),P(e,o),!1===n.dragStart(i,o))?(A(),!1):(D(e,o),l=null,o.useDefaultImage&&(a=f(i.span).find(".fancytree-title"),_&&1<_.length&&(l=f("<span class='fancytree-childcounter'/>").text("+"+(_.length-1)).appendTo(a)),r.setDragImage&&r.setDragImage(a[0],-10,-10)),!0);case"drag":P(e,o),n.dragDrag(i,o),D(e,o),k.toggleClass(c,o.isMove);break;case"dragend":P(e,o),A(),o.isCancelled=!E,n.dragEnd(i,o,!E)}}.bind(t)),r.dragEnter&&t.$container.on("dragenter dragover dragleave drop",j.bind(t))}}),f.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),t=function(d){"use strict";var t=/Mac/.test(navigator.platform),c=d.ui.fancytree.escapeHtml,u=d.ui.fancytree.trim,a=d.ui.fancytree.unescapeHtml;return d.ui.fancytree._FancytreeNodeClass.prototype.editStart=function(){var t,n=this,e=this.tree,i=e.ext.edit,r=e.options.edit,o=d(".fancytree-title",n.span),s={node:n,tree:e,options:e.options,isNew:d(n[e.statusClassPropName]).hasClass("fancytree-edit-new"),orgTitle:n.title,input:null,dirty:!1};if(!1===r.beforeEdit.call(n,{type:"beforeEdit"},s))return!1;d.ui.fancytree.assert(!i.currentNode,"recursive edit"),i.currentNode=this,i.eventData=s,e.widget._unbind(),i.lastDraggableAttrValue=n.span.draggable,i.lastDraggableAttrValue&&(n.span.draggable=!1),d(document).on("mousedown.fancytree-edit",function(e){d(e.target).hasClass("fancytree-edit-input")||n.editEnd(!0,e)}),t=d("<input />",{class:"fancytree-edit-input",type:"text",value:e.options.escapeTitles?s.orgTitle:a(s.orgTitle)}),i.eventData.input=t,null!=r.adjustWidthOfs&&t.width(o.width()+r.adjustWidthOfs),null!=r.inputCss&&t.css(r.inputCss),o.html(t),t.focus().change(function(e){t.addClass("fancytree-edit-dirty")}).on("keydown",function(e){switch(e.which){case d.ui.keyCode.ESCAPE:n.editEnd(!1,e);break;case d.ui.keyCode.ENTER:return n.editEnd(!0,e),!1}e.stopPropagation()}).blur(function(e){return n.editEnd(!0,e)}),r.edit.call(n,{type:"edit"},s)},d.ui.fancytree._FancytreeNodeClass.prototype.editEnd=function(e,t){var n,i=this,r=this.tree,o=r.ext.edit,s=o.eventData,a=r.options.edit,l=d(".fancytree-title",i.span).find("input.fancytree-edit-input");return a.trim&&l.val(u(l.val())),n=l.val(),s.dirty=n!==i.title,s.originalEvent=t,!1===e?s.save=!1:s.isNew?s.save=""!==n:s.save=s.dirty&&""!==n,!1!==a.beforeClose.call(i,{type:"beforeClose"},s)&&((!s.save||!1!==a.save.call(i,{type:"save"},s))&&(l.removeClass("fancytree-edit-dirty").off(),d(document).off(".fancytree-edit"),s.save?(i.setTitle(r.options.escapeTitles?n:c(n)),i.setFocus()):s.isNew?(i.remove(),i=s.node=null,o.relatedNode.setFocus()):(i.renderTitle(),i.setFocus()),o.eventData=null,o.currentNode=null,o.relatedNode=null,r.widget._bind(),i&&o.lastDraggableAttrValue&&(i.span.draggable=!0),r.$container.get(0).focus({preventScroll:!0}),s.input=null,a.close.call(i,{type:"close"},s),!0))},d.ui.fancytree._FancytreeNodeClass.prototype.editCreateNode=function(e,t){var n,i=this.tree,r=this;e=e||"child",null==t?t={title:""}:"string"==typeof t?t={title:t}:d.ui.fancytree.assert(d.isPlainObject(t)),"child"!==e||this.isExpanded()||!1===this.hasChildren()?((n=this.addNode(t,e)).match=!0,d(n[i.statusClassPropName]).removeClass("fancytree-hide").addClass("fancytree-match"),n.makeVisible().done(function(){d(n[i.statusClassPropName]).addClass("fancytree-edit-new"),r.tree.ext.edit.relatedNode=r,n.editStart()})):this.setExpanded().done(function(){r.editCreateNode(e,t)})},d.ui.fancytree._FancytreeClass.prototype.isEditing=function(){return this.ext.edit?this.ext.edit.currentNode:null},d.ui.fancytree._FancytreeNodeClass.prototype.isEditing=function(){return!!this.tree.ext.edit&&this.tree.ext.edit.currentNode===this},d.ui.fancytree.registerExtension({name:"edit",version:"2.38.2",options:{adjustWidthOfs:4,allowEmpty:!1,inputCss:{minWidth:"3em"},triggerStart:["f2","mac+enter","shift+click"],trim:!0,beforeClose:d.noop,beforeEdit:d.noop,close:d.noop,edit:d.noop,save:d.noop},currentNode:null,treeInit:function(e){var i=e.tree;this._superApply(arguments),this.$container.addClass("fancytree-ext-edit").on("fancytreebeforeupdateviewport",function(e,t){var n=i.isEditing();n&&(n.info("Cancel edit due to scroll event."),n.editEnd(!1,e))})},nodeClick:function(e){var t=d.ui.fancytree.eventToString(e.originalEvent),n=e.options.edit.triggerStart;return"shift+click"===t&&0<=d.inArray("shift+click",n)&&e.originalEvent.shiftKey||"click"===t&&0<=d.inArray("clickActive",n)&&e.node.isActive()&&!e.node.isEditing()&&d(e.originalEvent.target).hasClass("fancytree-title")?(e.node.editStart(),!1):this._superApply(arguments)},nodeDblclick:function(e){return 0<=d.inArray("dblclick",e.options.edit.triggerStart)?(e.node.editStart(),!1):this._superApply(arguments)},nodeKeydown:function(e){switch(e.originalEvent.which){case 113:if(0<=d.inArray("f2",e.options.edit.triggerStart))return e.node.editStart(),!1;break;case d.ui.keyCode.ENTER:if(0<=d.inArray("mac+enter",e.options.edit.triggerStart)&&t)return e.node.editStart(),!1}return this._superApply(arguments)}}),d.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),t=function(y){"use strict";var v="__not_found__",m=y.ui.fancytree.escapeHtml;function b(e){return(e+"").replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1")}function x(e,t,n){for(var i=[],r=1;r<t.length;r++){var o=t[r].length+(1===r?0:1)+(i[i.length-1]||0);i.push(o)}var s=e.split("");return n?i.forEach(function(e){s[e]="\ufff7"+s[e]+"\ufff8"}):i.forEach(function(e){s[e]="<mark>"+s[e]+"</mark>"}),s.join("")}return y.ui.fancytree._FancytreeClass.prototype._applyFilterImpl=function(i,r,e){var t,o,s,a,l,d,c=0,n=this.options,u=n.escapeTitles,h=n.autoCollapse,f=y.extend({},n.filter,e),p="hide"===f.mode,g=!!f.leavesOnly&&!r;if("string"==typeof i){if(""===i)return this.warn("Fancytree passing an empty string as a filter is handled as clearFilter()."),void this.clearFilter();t=f.fuzzy?i.split("").map(b).reduce(function(e,t){return e+"([^"+t+"]*)"+t},""):b(i),o=new RegExp(t,"i"),s=new RegExp(b(i),"gi"),u&&(a=new RegExp(b("\ufff7"),"g"),l=new RegExp(b("\ufff8"),"g")),i=function(e){if(!e.title)return!1;var t,n=u?e.title:0<=(t=e.title).indexOf(">")?y("<div/>").html(t).text():t,t=n.match(o);return t&&f.highlight&&(u?(d=f.fuzzy?x(n,t,u):n.replace(s,function(e){return"\ufff7"+e+"\ufff8"}),e.titleWithHighlight=m(d).replace(a,"<mark>").replace(l,"</mark>")):f.fuzzy?e.titleWithHighlight=x(n,t):e.titleWithHighlight=n.replace(s,function(e){return"<mark>"+e+"</mark>"})),!!t}}return this.enableFilter=!0,this.lastFilterArgs=arguments,e=this.enableUpdate(!1),this.$div.addClass("fancytree-ext-filter"),p?this.$div.addClass("fancytree-ext-filter-hide"):this.$div.addClass("fancytree-ext-filter-dimm"),this.$div.toggleClass("fancytree-ext-filter-hide-expanders",!!f.hideExpanders),this.rootNode.subMatchCount=0,this.visit(function(e){delete e.match,delete e.titleWithHighlight,e.subMatchCount=0}),(t=this.getRootNode()._findDirectChild(v))&&t.remove(),n.autoCollapse=!1,this.visit(function(t){if(!g||null==t.children){var e=i(t),n=!1;if("skip"===e)return t.visit(function(e){e.match=!1},!0),"skip";e||!r&&"branch"!==e||!t.parent.match||(n=e=!0),e&&(c++,t.match=!0,t.visitParents(function(e){e!==t&&(e.subMatchCount+=1),!f.autoExpand||n||e.expanded||(e.setExpanded(!0,{noAnimation:!0,noEvents:!0,scrollIntoView:!1}),e._filterAutoExpanded=!0)},!0))}}),n.autoCollapse=h,0===c&&f.nodata&&p&&(!0===(t="function"==typeof(t=f.nodata)?t():t)?t={}:"string"==typeof t&&(t={title:t}),t=y.extend({statusNodeType:"nodata",key:v,title:this.options.strings.noData},t),this.getRootNode().addNode(t).match=!0),this._callHook("treeStructureChanged",this,"applyFilter"),this.enableUpdate(e),c},y.ui.fancytree._FancytreeClass.prototype.filterNodes=function(e,t){return"boolean"==typeof t&&(t={leavesOnly:t},this.warn("Fancytree.filterNodes() leavesOnly option is deprecated since 2.9.0 / 2015-04-19. Use opts.leavesOnly instead.")),this._applyFilterImpl(e,!1,t)},y.ui.fancytree._FancytreeClass.prototype.filterBranches=function(e,t){return this._applyFilterImpl(e,!0,t)},y.ui.fancytree._FancytreeClass.prototype.updateFilter=function(){this.enableFilter&&this.lastFilterArgs&&this.options.filter.autoApply?this._applyFilterImpl.apply(this,this.lastFilterArgs):this.warn("updateFilter(): no filter active.")},y.ui.fancytree._FancytreeClass.prototype.clearFilter=function(){var t,e=this.getRootNode()._findDirectChild(v),n=this.options.escapeTitles,i=this.options.enhanceTitle,r=this.enableUpdate(!1);e&&e.remove(),delete this.rootNode.match,delete this.rootNode.subMatchCount,this.visit(function(e){e.match&&e.span&&(t=y(e.span).find(">span.fancytree-title"),n?t.text(e.title):t.html(e.title),i&&i({type:"enhanceTitle"},{node:e,$title:t})),delete e.match,delete e.subMatchCount,delete e.titleWithHighlight,e.$subMatchBadge&&(e.$subMatchBadge.remove(),delete e.$subMatchBadge),e._filterAutoExpanded&&e.expanded&&e.setExpanded(!1,{noAnimation:!0,noEvents:!0,scrollIntoView:!1}),delete e._filterAutoExpanded}),this.enableFilter=!1,this.lastFilterArgs=null,this.$div.removeClass("fancytree-ext-filter fancytree-ext-filter-dimm fancytree-ext-filter-hide"),this._callHook("treeStructureChanged",this,"clearFilter"),this.enableUpdate(r)},y.ui.fancytree._FancytreeClass.prototype.isFilterActive=function(){return!!this.enableFilter},y.ui.fancytree._FancytreeNodeClass.prototype.isMatched=function(){return!(this.tree.enableFilter&&!this.match)},y.ui.fancytree.registerExtension({name:"filter",version:"2.38.2",options:{autoApply:!0,autoExpand:!1,counter:!0,fuzzy:!1,hideExpandedCounter:!0,hideExpanders:!1,highlight:!0,leavesOnly:!1,nodata:!0,mode:"dimm"},nodeLoadChildren:function(e,t){var n=e.tree;return this._superApply(arguments).done(function(){n.enableFilter&&n.lastFilterArgs&&e.options.filter.autoApply&&n._applyFilterImpl.apply(n,n.lastFilterArgs)})},nodeSetExpanded:function(e,t,n){var i=e.node;return delete i._filterAutoExpanded,!t&&e.options.filter.hideExpandedCounter&&i.$subMatchBadge&&i.$subMatchBadge.show(),this._superApply(arguments)},nodeRenderStatus:function(e){var t=e.node,n=e.tree,i=e.options.filter,r=y(t.span).find("span.fancytree-title"),o=y(t[n.statusClassPropName]),s=e.options.enhanceTitle,a=e.options.escapeTitles,e=this._super(e);return o.length&&n.enableFilter&&(o.toggleClass("fancytree-match",!!t.match).toggleClass("fancytree-submatch",!!t.subMatchCount).toggleClass("fancytree-hide",!(t.match||t.subMatchCount)),!i.counter||!t.subMatchCount||t.isExpanded()&&i.hideExpandedCounter?t.$subMatchBadge&&t.$subMatchBadge.hide():(t.$subMatchBadge||(t.$subMatchBadge=y("<span class='fancytree-childcounter'/>"),y("span.fancytree-icon, span.fancytree-custom-icon",t.span).append(t.$subMatchBadge)),t.$subMatchBadge.show().text(t.subMatchCount)),!t.span||t.isEditing&&t.isEditing.call(t)||(t.titleWithHighlight?r.html(t.titleWithHighlight):a?r.text(t.title):r.html(t.title),s&&s({type:"enhanceTitle"},{node:t,$title:r}))),e}}),y.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),t=function(l){"use strict";var a=l.ui.fancytree,n={awesome3:{_addClass:"",checkbox:"icon-check-empty",checkboxSelected:"icon-check",checkboxUnknown:"icon-check icon-muted",dragHelper:"icon-caret-right",dropMarker:"icon-caret-right",error:"icon-exclamation-sign",expanderClosed:"icon-caret-right",expanderLazy:"icon-angle-right",expanderOpen:"icon-caret-down",loading:"icon-refresh icon-spin",nodata:"icon-meh",noExpander:"",radio:"icon-circle-blank",radioSelected:"icon-circle",doc:"icon-file-alt",docOpen:"icon-file-alt",folder:"icon-folder-close-alt",folderOpen:"icon-folder-open-alt"},awesome4:{_addClass:"fa",checkbox:"fa-square-o",checkboxSelected:"fa-check-square-o",checkboxUnknown:"fa-square fancytree-helper-indeterminate-cb",dragHelper:"fa-arrow-right",dropMarker:"fa-long-arrow-right",error:"fa-warning",expanderClosed:"fa-caret-right",expanderLazy:"fa-angle-right",expanderOpen:"fa-caret-down",loading:{html:"<span class='fa fa-spinner fa-pulse' />"},nodata:"fa-meh-o",noExpander:"",radio:"fa-circle-thin",radioSelected:"fa-circle",doc:"fa-file-o",docOpen:"fa-file-o",folder:"fa-folder-o",folderOpen:"fa-folder-open-o"},awesome5:{_addClass:"",checkbox:"far fa-square",checkboxSelected:"far fa-check-square",checkboxUnknown:"fas fa-square fancytree-helper-indeterminate-cb",radio:"far fa-circle",radioSelected:"fas fa-circle",radioUnknown:"far fa-dot-circle",dragHelper:"fas fa-arrow-right",dropMarker:"fas fa-long-arrow-alt-right",error:"fas fa-exclamation-triangle",expanderClosed:"fas fa-caret-right",expanderLazy:"fas fa-angle-right",expanderOpen:"fas fa-caret-down",loading:"fas fa-spinner fa-pulse",nodata:"far fa-meh",noExpander:"",doc:"far fa-file",docOpen:"far fa-file",folder:"far fa-folder",folderOpen:"far fa-folder-open"},bootstrap3:{_addClass:"glyphicon",checkbox:"glyphicon-unchecked",checkboxSelected:"glyphicon-check",checkboxUnknown:"glyphicon-expand fancytree-helper-indeterminate-cb",dragHelper:"glyphicon-play",dropMarker:"glyphicon-arrow-right",error:"glyphicon-warning-sign",expanderClosed:"glyphicon-menu-right",expanderLazy:"glyphicon-menu-right",expanderOpen:"glyphicon-menu-down",loading:"glyphicon-refresh fancytree-helper-spin",nodata:"glyphicon-info-sign",noExpander:"",radio:"glyphicon-remove-circle",radioSelected:"glyphicon-ok-circle",doc:"glyphicon-file",docOpen:"glyphicon-file",folder:"glyphicon-folder-close",folderOpen:"glyphicon-folder-open"},material:{_addClass:"material-icons",checkbox:{text:"check_box_outline_blank"},checkboxSelected:{text:"check_box"},checkboxUnknown:{text:"indeterminate_check_box"},dragHelper:{text:"play_arrow"},dropMarker:{text:"arrow-forward"},error:{text:"warning"},expanderClosed:{text:"chevron_right"},expanderLazy:{text:"last_page"},expanderOpen:{text:"expand_more"},loading:{text:"autorenew",addClass:"fancytree-helper-spin"},nodata:{text:"info"},noExpander:{text:""},radio:{text:"radio_button_unchecked"},radioSelected:{text:"radio_button_checked"},doc:{text:"insert_drive_file"},docOpen:{text:"insert_drive_file"},folder:{text:"folder"},folderOpen:{text:"folder_open"}}};function d(e,t,n,i,r){var o=i.map,s=o[r],a=l(t),i=a.find(".fancytree-childcounter"),o=n+" "+(o._addClass||"");"string"==typeof(s="function"==typeof s?s.call(this,e,t,r):s)?(t.innerHTML="",a.attr("class",o+" "+s).append(i)):s&&(s.text?t.textContent=""+s.text:s.html?t.innerHTML=s.html:t.innerHTML="",a.attr("class",o+" "+(s.addClass||"")).append(i))}return l.ui.fancytree.registerExtension({name:"glyph",version:"2.38.2",options:{preset:null,map:{}},treeInit:function(e){var t=e.tree,e=e.options.glyph;e.preset?(a.assert(!!n[e.preset],"Invalid value for `options.glyph.preset`: "+e.preset),e.map=l.extend({},n[e.preset],e.map)):t.warn("ext-glyph: missing `preset` option."),this._superApply(arguments),t.$container.addClass("fancytree-ext-glyph")},nodeRenderStatus:function(e){var t,n,i=e.node,r=l(i.span),o=e.options.glyph,s=this._super(e);return i.isRootNode()||((n=r.children(".fancytree-expander").get(0))&&(t=i.expanded&&i.hasChildren()?"expanderOpen":i.isUndefined()?"expanderLazy":i.hasChildren()?"expanderClosed":"noExpander",d(i,n,"fancytree-expander",o,t)),(n=(i.tr?l("td",i.tr).find(".fancytree-checkbox"):r.children(".fancytree-checkbox")).get(0))&&(e=a.evalOption("checkbox",i,i,o,!1),i.parent&&i.parent.radiogroup||"radio"===e?d(i,n,"fancytree-checkbox fancytree-radio",o,t=i.selected?"radioSelected":"radio"):d(i,n,"fancytree-checkbox",o,t=i.selected?"checkboxSelected":i.partsel?"checkboxUnknown":"checkbox")),(n=r.children(".fancytree-icon").get(0))&&(t=i.statusNodeType||(i.folder?i.expanded&&i.hasChildren()?"folderOpen":"folder":i.expanded?"docOpen":"doc"),d(i,n,"fancytree-icon",o,t))),s},nodeSetStatus:function(e,t,n,i){var r,o=e.options.glyph,s=e.node,e=this._superApply(arguments);return"error"!==t&&"loading"!==t&&"nodata"!==t||(s.parent?(r=l(".fancytree-expander",s.span).get(0))&&d(s,r,"fancytree-expander",o,t):(r=l(".fancytree-statusnode-"+t,s[this.nodeContainerAttrName]).find(".fancytree-icon").get(0))&&d(s,r,"fancytree-icon",o,t)),e}}),l.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),t=function(c){"use strict";var u=c.ui.keyCode,o={text:[u.UP,u.DOWN],checkbox:[u.UP,u.DOWN,u.LEFT,u.RIGHT],link:[u.UP,u.DOWN,u.LEFT,u.RIGHT],radiobutton:[u.UP,u.DOWN,u.LEFT,u.RIGHT],"select-one":[u.LEFT,u.RIGHT],"select-multiple":[u.LEFT,u.RIGHT]};function s(e,t){var n,i,r,o,s,a,l=e.closest("td"),d=null;switch(t){case u.LEFT:d=l.prev();break;case u.RIGHT:d=l.next();break;case u.UP:case u.DOWN:for(n=l.parent(),r=n,s=l.get(0),a=0,r.children().each(function(){return this!==s&&(o=c(this).prop("colspan"),void(a+=o||1))}),i=a;(n=t===u.UP?n.prev():n.next()).length&&(n.is(":hidden")||!(d=function(e,t){var n,i=null,r=0;return e.children().each(function(){return t<=r?(i=c(this),!1):(n=c(this).prop("colspan"),void(r+=n||1))}),i}(n,i))||!d.find(":input,a").length););}return d}return c.ui.fancytree.registerExtension({name:"gridnav",version:"2.38.2",options:{autofocusInput:!1,handleCursorKeys:!0},treeInit:function(n){this._requireExtension("table",!0,!0),this._superApply(arguments),this.$container.addClass("fancytree-ext-gridnav"),this.$container.on("focusin",function(e){var t=c.ui.fancytree.getNode(e.target);t&&!t.isActive()&&(e=n.tree._makeHookContext(t,e),n.tree._callHook("nodeSetActive",e,!0))})},nodeSetActive:function(e,t,n){var i=e.options.gridnav,r=e.node,o=e.originalEvent||{},o=c(o.target).is(":input");t=!1!==t,this._superApply(arguments),t&&(e.options.titlesTabbable?(o||(c(r.span).find("span.fancytree-title").focus(),r.setFocus()),e.tree.$container.attr("tabindex","-1")):i.autofocusInput&&!o&&c(r.tr||r.span).find(":input:enabled").first().focus())},nodeKeydown:function(e){var t,n,i=e.options.gridnav,r=e.originalEvent,e=c(r.target);return e.is(":input:enabled")?t=e.prop("type"):e.is("a")&&(t="link"),t&&i.handleCursorKeys?!((t=o[t])&&0<=c.inArray(r.which,t)&&(n=s(e,r.which))&&n.length)||(n.find(":input:enabled,a").focus(),!1):this._superApply(arguments)}}),c.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree","./jquery.fancytree.table"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree.table"),module.exports=t(require("jquery"))):t(jQuery),t=function(s){"use strict";return s.ui.fancytree.registerExtension({name:"multi",version:"2.38.2",options:{allowNoSelect:!1,mode:"sameParent"},treeInit:function(e){this._superApply(arguments),this.$container.addClass("fancytree-ext-multi"),1===e.options.selectMode&&s.error("Fancytree ext-multi: selectMode: 1 (single) is not compatible.")},nodeClick:function(e){var t=e.tree,n=e.node,i=t.getActiveNode()||t.getFirstChild(),r="checkbox"===e.targetType,o="expander"===e.targetType;switch(s.ui.fancytree.eventToString(e.originalEvent)){case"click":if(o)break;r||(t.selectAll(!1),n.setSelected());break;case"shift+click":t.visitRows(function(e){if(e.setSelected(),e===n)return!1},{start:i,reverse:i.isBelowOf(n)});break;case"ctrl+click":case"meta+click":return void n.toggleSelected()}return this._superApply(arguments)},nodeKeydown:function(e){var t=e.tree,n=e.node,i=e.originalEvent;switch(s.ui.fancytree.eventToString(i)){case"up":case"down":t.selectAll(!1),n.navigate(i.which,!0),t.getActiveNode().setSelected();break;case"shift+up":case"shift+down":n.navigate(i.which,!0),t.getActiveNode().setSelected()}return this._superApply(arguments)}}),s.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),t=function(p){"use strict";var t=null,n=null,i=null,r=p.ui.fancytree.assert,u="active",g="expanded",h="focus",f="selected";try{r(window.localStorage&&window.localStorage.getItem),n={get:function(e){return window.localStorage.getItem(e)},set:function(e,t){window.localStorage.setItem(e,t)},remove:function(e){window.localStorage.removeItem(e)}}}catch(e){p.ui.fancytree.warn("Could not access window.localStorage",e)}try{r(window.sessionStorage&&window.sessionStorage.getItem),i={get:function(e){return window.sessionStorage.getItem(e)},set:function(e,t){window.sessionStorage.setItem(e,t)},remove:function(e){window.sessionStorage.removeItem(e)}}}catch(e){p.ui.fancytree.warn("Could not access window.sessionStorage",e)}return"function"==typeof Cookies?t={get:Cookies.get,set:function(e,t){Cookies.set(e,t,this.options.persist.cookie)},remove:Cookies.remove}:p&&"function"==typeof p.cookie&&(t={get:p.cookie,set:function(e,t){p.cookie(e,t,this.options.persist.cookie)},remove:p.removeCookie}),p.ui.fancytree._FancytreeClass.prototype.clearPersistData=function(e){var t=this.ext.persist,n=t.cookiePrefix;0<=(e=e||"active expanded focus selected").indexOf(u)&&t._data(n+u,null),0<=e.indexOf(g)&&t._data(n+g,null),0<=e.indexOf(h)&&t._data(n+h,null),0<=e.indexOf(f)&&t._data(n+f,null)},p.ui.fancytree._FancytreeClass.prototype.clearCookies=function(e){return this.warn("'tree.clearCookies()' is deprecated since v2.27.0: use 'clearPersistData()' instead."),this.clearPersistData(e)},p.ui.fancytree._FancytreeClass.prototype.getPersistData=function(){var e=this.ext.persist,t=e.cookiePrefix,n=e.cookieDelimiter,i={};return i[u]=e._data(t+u),i[g]=(e._data(t+g)||"").split(n),i[f]=(e._data(t+f)||"").split(n),i[h]=e._data(t+h),i},p.ui.fancytree.registerExtension({name:"persist",version:"2.38.2",options:{cookieDelimiter:"~",cookiePrefix:void 0,cookie:{raw:!1,expires:"",path:"",domain:"",secure:!1},expandLazy:!1,expandOpts:void 0,fireActivate:!0,overrideSource:!0,store:"auto",types:"active expanded focus selected"},_data:function(e,t){var n=this._local.store;if(void 0===t)return n.get.call(this,e);null===t?n.remove.call(this,e):n.set.call(this,e,t)},_appendKey:function(e,t,n){t=""+t;var i=this._local,r=this.options.persist.cookieDelimiter,o=i.cookiePrefix+e,s=i._data(o),e=s?s.split(r):[],s=p.inArray(t,e);0<=s&&e.splice(s,1),n&&e.push(t),i._data(o,e.join(r))},treeInit:function(e){var a=e.tree,l=e.options,d=this._local,c=this.options.persist;return d.cookiePrefix=c.cookiePrefix||"fancytree-"+a._id+"-",d.storeActive=0<=c.types.indexOf(u),d.storeExpanded=0<=c.types.indexOf(g),d.storeSelected=0<=c.types.indexOf(f),d.storeFocus=0<=c.types.indexOf(h),d.store=null,"auto"===c.store&&(c.store=n?"local":"cookie"),p.isPlainObject(c.store)?d.store=c.store:"cookie"===c.store?d.store=t:"local"!==c.store&&"session"!==c.store||(d.store="local"===c.store?n:i),r(d.store,"Need a valid store."),a.$div.on("fancytreeinit",function(e){var t,n,i,r,o,s;!1!==a._triggerTreeEvent("beforeRestore",null,{})&&(i=d._data(d.cookiePrefix+h),r=!1===c.fireActivate,o=d._data(d.cookiePrefix+g),s=o&&o.split(c.cookieDelimiter),(d.storeExpanded?function e(t,n,i,r,o){var s,a,l,d,c=!1,u=t.options.persist.expandOpts,h=[],f=[];for(i=i||[],o=o||p.Deferred(),s=0,l=i.length;s<l;s++)a=i[s],(d=t.getNodeByKey(a))?r&&d.isUndefined()?(c=!0,t.debug("_loadLazyNodes: "+d+" is lazy: loading..."),"expand"===r?h.push(d.setExpanded(!0,u)):h.push(d.load())):(t.debug("_loadLazyNodes: "+d+" already loaded."),d.setExpanded(!0,u)):(f.push(a),t.debug("_loadLazyNodes: "+d+" was not yet found."));return p.when.apply(p,h).always(function(){if(c&&0<f.length)e(t,n,f,r,o);else{if(f.length)for(t.warn("_loadLazyNodes: could not load those keys: ",f),s=0,l=f.length;s<l;s++)a=i[s],n._appendKey(g,i[s],!1);o.resolve()}}),o}(a,d,s,!!c.expandLazy&&"expand",null):(new p.Deferred).resolve()).done(function(){if(d.storeSelected){if(o=d._data(d.cookiePrefix+f))for(s=o.split(c.cookieDelimiter),t=0;t<s.length;t++)(n=a.getNodeByKey(s[t]))?(void 0===n.selected||c.overrideSource&&!1===n.selected)&&(n.selected=!0,n.renderStatus()):d._appendKey(f,s[t],!1);3===a.options.selectMode&&a.visit(function(e){if(e.selected)return e.fixSelection3AfterClick(),"skip"})}d.storeActive&&(!(o=d._data(d.cookiePrefix+u))||!l.persist.overrideSource&&a.activeNode||(n=a.getNodeByKey(o))&&(n.debug("persist: set active",o),n.setActive(!0,{noFocus:!0,noEvents:r}))),d.storeFocus&&i&&(n=a.getNodeByKey(i))&&(a.options.titlesTabbable?p(n.span).find(".fancytree-title"):p(a.$container)).focus(),a._triggerTreeEvent("restore",null,{})}))}),this._superApply(arguments)},nodeSetActive:function(e,t,n){var i=this._local;return t=!1!==t,t=this._superApply(arguments),i.storeActive&&i._data(i.cookiePrefix+u,this.activeNode?this.activeNode.key:null),t},nodeSetExpanded:function(e,t,n){var i=e.node,r=this._local;return t=!1!==t,e=this._superApply(arguments),r.storeExpanded&&r._appendKey(g,i.key,t),e},nodeSetFocus:function(e,t){var n=this._local;return t=!1!==t,t=this._superApply(arguments),n.storeFocus&&n._data(n.cookiePrefix+h,this.focusNode?this.focusNode.key:null),t},nodeSetSelected:function(e,t,n){var i=e.tree,r=e.node,o=this._local;return t=!1!==t,t=this._superApply(arguments),o.storeSelected&&(3===i.options.selectMode?(i=(i=p.map(i.getSelectedNodes(!0),function(e){return e.key})).join(e.options.persist.cookieDelimiter),o._data(o.cookiePrefix+f,i)):o._appendKey(f,r.key,r.selected)),t}}),p.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),t=function(m){"use strict";var b=m.ui.fancytree.assert;function x(e,n){e.visit(function(e){var t=e.tr;if(t&&(t.style.display=e.hide||!n?"none":""),!e.expanded)return"skip"})}return m.ui.fancytree.registerExtension({name:"table",version:"2.38.2",options:{checkboxColumnIdx:null,indentation:16,mergeStatusColumns:!0,nodeColumnIdx:0},treeInit:function(e){var t,n,i,r=e.tree,o=e.options,s=o.table,a=r.widget.element;if(null!=s.customStatus&&(null==o.renderStatusColumns?(r.warn("The 'customStatus' option is deprecated since v2.15.0. Use 'renderStatusColumns' instead."),o.renderStatusColumns=s.customStatus):m.error("The 'customStatus' option is deprecated since v2.15.0. Use 'renderStatusColumns' only instead.")),o.renderStatusColumns&&!0===o.renderStatusColumns&&(o.renderStatusColumns=o.renderColumns),a.addClass("fancytree-container fancytree-ext-table"),(i=a.find(">tbody")).length||(a.find(">tr").length&&m.error("Expected table > tbody > tr. If you see this please open an issue."),i=m("<tbody>").appendTo(a)),r.tbody=i[0],r.columnCount=m("thead >tr",a).last().find(">th",a).length,(n=i.children("tr").first()).length)e=n.children("td").length,r.columnCount&&e!==r.columnCount&&(r.warn("Column count mismatch between thead ("+r.columnCount+") and tbody ("+e+"): using tbody."),r.columnCount=e),n=n.clone();else for(b(1<=r.columnCount,"Need either <thead> or <tbody> with <td> elements to determine column count."),n=m("<tr />"),t=0;t<r.columnCount;t++)n.append("<td />");n.find(">td").eq(s.nodeColumnIdx).html("<span class='fancytree-node' />"),o.aria&&(n.attr("role","row"),n.find("td").attr("role","gridcell")),r.rowFragment=document.createDocumentFragment(),r.rowFragment.appendChild(n.get(0)),i.empty(),r.statusClassPropName="tr",r.ariaPropName="tr",this.nodeContainerAttrName="tr",r.$container=a,this._superApply(arguments),m(r.rootNode.ul).remove(),r.rootNode.ul=null,this.$container.attr("tabindex",o.tabindex),o.aria&&r.$container.attr("role","treegrid").attr("aria-readonly",!0)},nodeRemoveChildMarkup:function(e){e.node.visit(function(e){e.tr&&(m(e.tr).remove(),e.tr=null)})},nodeRemoveMarkup:function(e){var t=e.node;t.tr&&(m(t.tr).remove(),t.tr=null),this.nodeRemoveChildMarkup(e)},nodeRender:function(e,t,n,i,r){var o,s,a,l,d,c,u,h,f,p=e.tree,g=e.node,y=e.options,v=!g.parent;if(!1!==p._enableUpdate){if(r||(e.hasCollapsedParents=g.parent&&!g.parent.expanded),!v)if(g.tr&&t&&this.nodeRemoveMarkup(e),g.tr)t?this.nodeRenderTitle(e):this.nodeRenderStatus(e);else{if(e.hasCollapsedParents&&!n)return;d=p.rowFragment.firstChild.cloneNode(!0),h=function(e){var t,n,i=e.parent,r=i?i.children:null;if(r&&1<r.length&&r[0]!==e)for(n=r[m.inArray(e,r)-1],b(n.tr);n.children&&n.children.length&&(t=n.children[n.children.length-1]).tr;)n=t;else n=i;return n}(g),b(h),(!0===i&&r||n&&e.hasCollapsedParents)&&(d.style.display="none"),h.tr?(f=h.tr).parentNode.insertBefore(d,f.nextSibling):(b(!h.parent,"prev. row must have a tr, or be system root"),(h=p.tbody).insertBefore(d,h.firstChild)),g.tr=d,g.key&&y.generateIds&&(g.tr.id=y.idPrefix+g.key),(g.tr.ftnode=g).span=m("span.fancytree-node",g.tr).get(0),this.nodeRenderTitle(e),y.createNode&&y.createNode.call(p,{type:"createNode"},e)}if(y.renderNode&&y.renderNode.call(p,{type:"renderNode"},e),(o=g.children)&&(v||n||g.expanded))for(a=0,l=o.length;a<l;a++)(u=m.extend({},e,{node:o[a]})).hasCollapsedParents=u.hasCollapsedParents||!g.expanded,this.nodeRender(u,t,n,i,!0);o&&!r&&(c=g.tr||null,s=p.tbody.firstChild,g.visit(function(e){var t;e.tr&&(e.parent.expanded||"none"===e.tr.style.display||(e.tr.style.display="none",x(e,!1)),e.tr.previousSibling!==c&&(g.debug("_fixOrder: mismatch at node: "+e),t=c?c.nextSibling:s,p.tbody.insertBefore(e.tr,t)),c=e.tr)}))}},nodeRenderTitle:function(e,t){var n=e.tree,i=e.node,r=e.options,o=i.isStatusNode(),s=this._super(e,t);return i.isRootNode()||(r.checkbox&&!o&&null!=r.table.checkboxColumnIdx&&(t=m("span.fancytree-checkbox",i.span),m(i.tr).find("td").eq(+r.table.checkboxColumnIdx).html(t)),this.nodeRenderStatus(e),o?r.renderStatusColumns?r.renderStatusColumns.call(n,{type:"renderStatusColumns"},e):r.table.mergeStatusColumns&&i.isTopLevel()&&m(i.tr).find(">td").eq(0).prop("colspan",n.columnCount).text(i.title).addClass("fancytree-status-merged").nextAll().remove():r.renderColumns&&r.renderColumns.call(n,{type:"renderColumns"},e)),s},nodeRenderStatus:function(e){var t=e.node,n=e.options;this._super(e),m(t.tr).removeClass("fancytree-node"),e=(t.getLevel()-1)*n.table.indentation,n.rtl?m(t.span).css({paddingRight:e+"px"}):m(t.span).css({paddingLeft:e+"px"})},nodeSetExpanded:function(t,n,i){if(n=!1!==n,t.node.expanded&&n||!t.node.expanded&&!n)return this._superApply(arguments);var r=new m.Deferred,e=m.extend({},i,{noEvents:!0,noAnimation:!0});function o(e){e?(x(t.node,n),n&&t.options.autoScroll&&!i.noAnimation&&t.node.hasChildren()?t.node.getLastChild().scrollIntoView(!0,{topNode:t.node}).always(function(){i.noEvents||t.tree._triggerNodeEvent(n?"expand":"collapse",t),r.resolveWith(t.node)}):(i.noEvents||t.tree._triggerNodeEvent(n?"expand":"collapse",t),r.resolveWith(t.node))):(i.noEvents||t.tree._triggerNodeEvent(n?"expand":"collapse",t),r.rejectWith(t.node))}return i=i||{},this._super(t,n,e).done(function(){o(!0)}).fail(function(){o(!1)}),r.promise()},nodeSetStatus:function(e,t,n,i){return"ok"!==t||(e=(e=e.node).children?e.children[0]:null)&&e.isStatusNode()&&m(e.tr).remove(),this._superApply(arguments)},treeClear:function(e){return this.nodeRemoveChildMarkup(this._makeHookContext(this.rootNode)),this._superApply(arguments)},treeDestroy:function(e){return this.$container.find("tbody").empty(),this.$source&&this.$source.removeClass("fancytree-helper-hidden"),this._superApply(arguments)}}),m.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),t=function(o){"use strict";return o.ui.fancytree.registerExtension({name:"themeroller",version:"2.38.2",options:{activeClass:"ui-state-active",addClass:"ui-corner-all",focusClass:"ui-state-focus",hoverClass:"ui-state-hover",selectedClass:"ui-state-highlight"},treeInit:function(e){var t=e.widget.element,n=e.options.themeroller;this._superApply(arguments),"TABLE"===t[0].nodeName?(t.addClass("ui-widget ui-corner-all"),t.find(">thead tr").addClass("ui-widget-header"),t.find(">tbody").addClass("ui-widget-conent")):t.addClass("ui-widget ui-widget-content ui-corner-all"),t.on("mouseenter mouseleave",".fancytree-node",function(e){var t=o.ui.fancytree.getNode(e.target),e="mouseenter"===e.type;o(t.tr||t.span).toggleClass(n.hoverClass+" "+n.addClass,e)})},treeDestroy:function(e){this._superApply(arguments),e.widget.element.removeClass("ui-widget ui-widget-content ui-corner-all")},nodeRenderStatus:function(e){var t={},n=e.node,i=o(n.tr||n.span),r=e.options.themeroller;this._super(e),t[r.activeClass]=!1,t[r.focusClass]=!1,t[r.selectedClass]=!1,n.isActive()&&(t[r.activeClass]=!0),n.hasFocus()&&(t[r.focusClass]=!0),n.isSelected()&&!n.isActive()&&(t[r.selectedClass]=!0),i.toggleClass(r.activeClass,t[r.activeClass]),i.toggleClass(r.focusClass,t[r.focusClass]),i.toggleClass(r.selectedClass,t[r.selectedClass]),i.addClass(r.addClass)}}),o.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),t=function(d){"use strict";var c=/^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;function u(e,t){var n=d("#"+(e="fancytree-style-"+e));if(t){n.length||(n=d("<style />").attr("id",e).addClass("fancytree-style").prop("type","text/css").appendTo("head"));try{n.html(t)}catch(e){n[0].styleSheet.cssText=t}return n}n.remove()}function h(e,t,n,i,r,o){for(var s="#"+e+" span.fancytree-level-",a=[],l=0;l<t;l++)a.push(s+(l+1)+" span.fancytree-title { padding-left: "+(l*n+i)+o+"; }");return a.push("#"+e+" div.ui-effects-wrapper ul li span.fancytree-title, #"+e+" li.fancytree-animating span.fancytree-title { padding-left: "+r+o+"; position: static; width: auto; }"),a.join("\n")}return d.ui.fancytree.registerExtension({name:"wide",version:"2.38.2",options:{iconWidth:null,iconSpacing:null,labelSpacing:null,levelOfs:null},treeCreate:function(e){this._superApply(arguments),this.$container.addClass("fancytree-ext-wide");var t=e.options.wide,n=d("<li id='fancytreeTemp'><span class='fancytree-node'><span class='fancytree-icon' /><span class='fancytree-title' /></span><ul />").appendTo(e.tree.$container),i=n.find(".fancytree-icon"),r=n.find("ul"),o=t.iconSpacing||i.css("margin-left"),s=t.iconWidth||i.css("width"),a=t.labelSpacing||"3px",l=t.levelOfs||r.css("padding-left");n.remove(),i=o.match(c)[2],o=parseFloat(o,10),t=a.match(c)[2],a=parseFloat(a,10),r=s.match(c)[2],s=parseFloat(s,10),n=l.match(c)[2],i===r&&n===r&&t===r||d.error("iconWidth, iconSpacing, and levelOfs must have the same css measure unit"),this._local.measureUnit=r,this._local.levelOfs=parseFloat(l),this._local.lineOfs=(1+(e.options.checkbox?1:0)+(!1===e.options.icon?0:1))*(s+o)+o,this._local.labelOfs=a,this._local.maxDepth=10,u(a=this.$container.uniqueId().attr("id"),h(a,this._local.maxDepth,this._local.levelOfs,this._local.lineOfs,this._local.labelOfs,this._local.measureUnit))},treeDestroy:function(e){return u(this.$container.attr("id"),null),this._superApply(arguments)},nodeRenderStatus:function(e){var t=e.node,n=t.getLevel(),i=this._super(e);return n>this._local.maxDepth&&(e=this.$container.attr("id"),this._local.maxDepth*=2,t.debug("Define global ext-wide css up to level "+this._local.maxDepth),u(e,h(e,this._local.maxDepth,this._local.levelOfs,this._local.lineOfs,this._local.labelSpacing,this._local.measureUnit))),d(t.span).addClass("fancytree-level-"+n),i}}),d.ui.fancytree},"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],t):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=t(require("jquery"))):t(jQuery),e.ui.fancytree});
//# sourceMappingURL=jquery.fancytree-all-deps.min.js.map