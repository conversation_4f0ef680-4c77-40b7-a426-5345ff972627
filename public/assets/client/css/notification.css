.containers {
    display: flex;
    width: 100%;
    height: 100vh;
}

.filter-panel {
    width: 25%;
    background-color: #fff;
    border-right: 1px solid #e5e5e5;
    padding: 20px;
}

.filter-panel h2 {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
}

.filter-panel ul {
    list-style: none;
    padding: 0;
}

.filter-panel ul li {
    margin-bottom: 10px;
}

.filter-panel ul li label {
    font-size: 14px;
    color: #555;
    cursor: pointer;
}

.filter-panel ul li input {
    margin-right: 10px;
}

.notification-panel {
    width: 75%;
    padding: 20px;
    overflow-y: auto;
}

.notification-panel h2 {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
}

.notification {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notification img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
}

.notification-content {
    flex: 1;
}

.notification-content h3 {
    margin: 0;
    font-size: 16px;
    color: #007bff;
}

.notification-content p {
    margin: 5px 0 0;
    font-size: 14px;
    color: #555;
}

.notification-content time {
    font-size: 12px;
    color: #888;
}

.notification-status {
    color: white;
    background-color: #007bff;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
}

/* Thông báo mới */
.notification.new {
    background-color: #d4f4dd; /* Màu xanh nhạt */
    border: 1px solid #4caf50;
}

/* Thông báo cũ */
.notification.old {
    background-color: #f9d4d4; /* Màu đỏ nhạt */
    border: 1px solid #f44336;
}

/* Căn chỉnh thông báo */
.notification {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
}

/* Thông báo mới */
.notification.new {
    background-color: #d4f4dd; /* Màu xanh nhạt */
    border: 1px solid #4caf50;
}

/* Thông báo cũ */
.notification.old {
    background-color: #f9d4d4; /* Màu đỏ nhạt */
    border: 1px solid #f44336;
}

/* Căn chỉnh thông báo */
.notification {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
}

/* Điều hướng phân trang */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.pagination button {
    padding: 10px 20px;
    margin: 0 5px;
    border: none;
    background-color: #4caf50;
    color: white;
    border-radius: 5px;
    cursor: pointer;
}

.pagination button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}
